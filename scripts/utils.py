#!/usr/bin/env python3
"""
Utilities for Magnetic-One Test Script (M1)
=====================================

This module contains utility functions for logging and data processing used by the Magnetic-One test script.
"""

import logging
import json
from pathlib import Path


def setup_llm_logging(scenario_id: str, endpoint_type: str, model: str, output_dir: Path) -> Path:
    """
    设置LLM调用的详细日志记录，保存所有输入给LLM的消息和token统计
    每次调用会初始化一个新的日志文件，确保每次任务的日志独立保存

    Args:
        scenario_id: 场景ID (如 "1")
        endpoint_type: 端点类型 (如 "ollama", "openai")
        model: 模型名称 (如 "llama3.1")
        output_dir: 输出目录路径

    Returns:
        Path: LLM日志文件的路径
    """
    from datetime import datetime, timezone

    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建LLM调用日志文件路径，与现有日志命名保持一致
    llm_log_path = output_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_llm_calls.log"

    # 初始化日志文件 - 使用UTC时间
    start_time = datetime.now(timezone.utc)
    start_msg = f"=== Started LLM logging for scenario {scenario_id} with {endpoint_type} at {start_time.strftime('%H:%M:%S')} UTC ===\n"
    start_msg += f"=== Model: {model} ===\n"
    start_msg += f"=== This log contains all LLM calls with complete input messages and token statistics ===\n\n"

    # 写入初始化信息（覆盖模式，确保每次任务都是新的日志）
    llm_log_path.write_text(start_msg, encoding="utf-8")

    # 配置日志 - 简化版本，不添加时间戳
    import logging

    # 创建简单的格式化器，只保存消息内容
    class SimpleFormatter(logging.Formatter):
        def format(self, record):
            # 直接返回消息内容，不添加时间戳
            return str(record.msg)

    # 清除现有的handlers，避免重复配置
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # 配置日志只保存消息内容
    handler = logging.FileHandler(llm_log_path, mode='a', encoding='utf-8')
    handler.setLevel(logging.INFO)
    handler.setFormatter(SimpleFormatter())

    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.addHandler(handler)

    # 确保autogen事件被记录
    # 这会捕获所有LLM调用，包括完整的输入消息和token统计
    logging.getLogger("autogen_core.events").setLevel(logging.INFO)

    print(f"✓ LLM调用详细日志已初始化，将保存到: {llm_log_path}")
    print(f"✓ 将记录所有输入给LLM的完整消息内容和token使用统计")
    print(f"✓ 每次运行都会创建新的日志文件，避免多次调试混淆")
    return llm_log_path


def format_llm_calls_log(llm_log_path: Path) -> Path:
    """
    将LLM调用日志格式化为结构化的JSON格式

    Args:
        llm_log_path: 原始LLM日志文件路径

    Returns:
        Path: 格式化后的JSON文件路径
    """
    if not llm_log_path.exists():
        print(f"⚠️  LLM日志文件不存在: {llm_log_path}")
        return llm_log_path

    # 创建格式化后的JSON文件路径
    formatted_path = llm_log_path.with_name(llm_log_path.stem + '_formatted.json')

    # 用于存储格式化后的数据
    formatted_data = {
        "metadata": {
            "source_file": str(llm_log_path)
        },
        "llm_calls": []
    }

    call_count = 0
    total_prompt_tokens = 0
    total_completion_tokens = 0

    with open(llm_log_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()

            # Skip empty lines and header/footer lines
            if not line or line.startswith('==='):
                continue

            # Try to parse JSON log entry (no timestamp prefix now)
            try:
                # 直接解析JSON内容
                if line.startswith('{') and line.endswith('}'):
                    log_data = json.loads(line)
                else:
                    continue

                if log_data.get('type') != 'LLMCall':
                    continue

                call_count += 1
                prompt_tokens = log_data.get('prompt_tokens', 0)
                completion_tokens = log_data.get('completion_tokens', 0)
                total_prompt_tokens += prompt_tokens
                total_completion_tokens += completion_tokens

                # 处理输入消息 - 保持原生态记录
                messages = log_data.get('messages', [])
                formatted_messages = []
                for i, msg in enumerate(messages, 1):
                    formatted_messages.append({
                        "message_index": i,
                        "role": msg.get('role', 'unknown'),
                        "content": msg.get('content', ''),
                        "thinking": msg.get('thinking'),
                        "images": msg.get('images'),
                        "tool_calls": msg.get('tool_calls')
                    })

                # 处理响应 - 保持原生态记录
                response = log_data.get('response', {})
                formatted_response = {
                    "model": response.get('model'),
                    "created_at": response.get('created_at'),
                    "done": response.get('done'),
                    "done_reason": response.get('done_reason'),
                    "total_duration": response.get('total_duration'),
                    "load_duration": response.get('load_duration'),
                    "prompt_eval_count": response.get('prompt_eval_count'),
                    "prompt_eval_duration": response.get('prompt_eval_duration'),
                    "eval_count": response.get('eval_count'),
                    "eval_duration": response.get('eval_duration'),
                    "message": response.get('message', {})
                }

                # 从response中提取时间戳
                response = log_data.get('response', {})
                timestamp = response.get('created_at')

                # 创建格式化的调用记录 - 保持原生态
                formatted_call = {
                    "call_index": call_count,
                    "timestamp": timestamp,
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "input_messages": formatted_messages,
                    "response": formatted_response,
                    "agent_id": log_data.get('agent_id')
                }

                formatted_data["llm_calls"].append(formatted_call)

            except Exception as e:
                if "parsing_errors" not in formatted_data:
                    formatted_data["parsing_errors"] = []
                formatted_data["parsing_errors"].append({
                    "line": line_num,
                    "error": str(e)
                })

    # 填充元数据 - 使用日志中的时间信息
    formatted_data["metadata"]["total_calls"] = call_count
    formatted_data["metadata"]["total_prompt_tokens"] = total_prompt_tokens
    formatted_data["metadata"]["total_completion_tokens"] = total_completion_tokens
    formatted_data["metadata"]["total_tokens"] = total_prompt_tokens + total_completion_tokens

    # 从第一个和最后一个LLM调用中提取时间信息
    if formatted_data["llm_calls"]:
        first_call = formatted_data["llm_calls"][0]
        last_call = formatted_data["llm_calls"][-1]
        formatted_data["metadata"]["first_call_timestamp"] = first_call["timestamp"]
        formatted_data["metadata"]["last_call_timestamp"] = last_call["timestamp"]
        if first_call["response"].get("created_at"):
            formatted_data["metadata"]["session_start"] = first_call["response"]["created_at"]
        if last_call["response"].get("created_at"):
            formatted_data["metadata"]["session_end"] = last_call["response"]["created_at"]

    # 写入JSON文件
    with open(formatted_path, 'w', encoding='utf-8') as f:
        json.dump(formatted_data, f, indent=2, ensure_ascii=False)

    return formatted_path


def format_complete_log(llm_log_path: Path) -> Path:
    """
    将完整的日志格式化为易读的JSON格式，保留所有信息

    Args:
        llm_log_path: 原始日志文件路径

    Returns:
        Path: 格式化后的完整日志JSON文件路径
    """
    if not llm_log_path.exists():
        print(f"⚠️  日志文件不存在: {llm_log_path}")
        return llm_log_path

    # 创建格式化后的完整日志JSON文件路径
    # 从 scenario_1_ollama_llama3.1_llm_calls.log 变成 scenario_1_ollama_llama3.1_log_formatted.json
    base_name = llm_log_path.stem.replace('_llm_calls', '')
    formatted_path = llm_log_path.with_name(base_name + '_log_formatted.json')

    # 用于存储格式化后的完整数据
    formatted_data = []

    with open(llm_log_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()

            # 跳过空行和标题行
            if not line or line.startswith('==='):
                continue

            # 处理日志条目（现在没有时间戳前缀）
            # 尝试解析JSON内容
            if line.startswith('{') and line.endswith('}'):
                try:
                    parsed_content = json.loads(line)
                    # 尝试从内容中提取时间戳
                    timestamp = None
                    if parsed_content.get('type') == 'LLMCall':
                        # 从LLM调用的response中提取时间
                        response = parsed_content.get('response', {})
                        timestamp = response.get('created_at')
                    elif parsed_content.get('type') == 'Message':
                        # 从Message的payload中尝试提取时间
                        try:
                            payload = json.loads(parsed_content.get('payload', '{}'))
                            messages = payload.get('messages', [])
                            if messages and len(messages) > 0:
                                timestamp = messages[0].get('created_at')
                        except:
                            pass

                    formatted_entry = {
                        "line_number": line_num,
                        "timestamp": timestamp,
                        "content": parsed_content
                    }
                except json.JSONDecodeError:
                    # JSON解析失败，作为普通文本处理
                    formatted_entry = {
                        "line_number": line_num,
                        "timestamp": None,
                        "content": line
                    }
            else:
                # 非JSON内容，作为普通文本处理
                formatted_entry = {
                    "line_number": line_num,
                    "timestamp": None,
                    "content": line
                }

            formatted_data.append(formatted_entry)

    # 写入JSON文件，使用美化格式
    with open(formatted_path, 'w', encoding='utf-8') as f:
        json.dump(formatted_data, f, indent=2, ensure_ascii=False)

    return formatted_path


def finalize_llm_logging(llm_log_path: Path, total_steps: int, duration: str) -> None:
    """
    在任务完成时添加结束标记到LLM日志文件，并生成两种格式化版本

    Args:
        llm_log_path: LLM日志文件路径
        total_steps: 总步数
        duration: 执行时长
    """
    from datetime import datetime, timezone

    end_time = datetime.now(timezone.utc)
    end_msg = f"\n=== Completed LLM logging at {end_time.strftime('%H:%M:%S')} UTC ===\n"
    end_msg += f"=== Total steps: {total_steps} | Duration: {duration} ===\n"
    end_msg += f"=== End of LLM calls log ===\n"

    # 追加结束信息
    with llm_log_path.open("a", encoding="utf-8") as f:
        f.write(end_msg)

    # 生成两种格式化版本
    llm_formatted_path = format_llm_calls_log(llm_log_path)
    complete_formatted_path = format_complete_log(llm_log_path)

    print(f"✓ LLM日志记录完成，共记录 {total_steps} 个步骤，耗时 {duration}")
    print(f"✓ 原始日志: {llm_log_path}")
    print(f"✓ LLM调用格式化: {llm_formatted_path}")
    print(f"✓ 完整日志格式化: {complete_formatted_path}")




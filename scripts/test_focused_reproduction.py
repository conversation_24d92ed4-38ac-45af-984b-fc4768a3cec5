#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
test_focused_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~
测试专注断点复现功能的脚本

功能：
1. 验证断点复现功能的基本工作流程
2. 测试不同场景和断点位置的处理
3. 检查错误处理和边界条件

使用方法：
    python test_focused_reproduction.py
"""

import asyncio
import sys
from pathlib import Path

# 添加脚本目录到路径
sys.path.append(str(Path(__file__).parent))

from focused_breakpoint_reproduction import (
    Config, 
    ScenarioLoader, 
    BreakpointReproducer
)

async def test_scenario_loading():
    """测试场景数据加载功能"""
    print("=== 测试场景数据加载 ===")
    
    loader = ScenarioLoader()
    
    try:
        # 测试加载场景1
        data = loader.load_scenario("1")
        print(f"✓ 场景1加载成功，包含 {len(data['history'])} 步")
        
        # 测试断点验证
        loader.validate_breakpoint(data["history"], 5)
        print("✓ 断点验证通过")
        
        # 测试无效断点
        try:
            loader.validate_breakpoint(data["history"], 999)
            print("❌ 应该抛出异常但没有")
        except ValueError:
            print("✓ 无效断点正确被拒绝")
        
    except Exception as e:
        print(f"❌ 场景加载测试失败: {e}")
        return False
    
    return True

async def test_config_validation():
    """测试配置验证功能"""
    print("\n=== 测试配置验证 ===")
    
    try:
        Config.validate()
        print("✓ 配置验证通过")
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        print("请确保设置了 OPENAI_API_KEY 环境变量")
        return False

async def test_dry_run():
    """测试干运行（不实际调用API）"""
    print("\n=== 测试干运行 ===")
    
    # 检查数据文件是否存在
    test_scenarios = ["1", "2", "3"]
    available_scenarios = []
    
    for scenario_id in test_scenarios:
        scenario_path = Config.DATA_DIR / f"{scenario_id}.json"
        if scenario_path.exists():
            available_scenarios.append(scenario_id)
            print(f"✓ 场景 {scenario_id} 数据文件存在")
        else:
            print(f"⚠ 场景 {scenario_id} 数据文件不存在: {scenario_path}")
    
    if not available_scenarios:
        print("❌ 没有找到可用的测试场景")
        return False
    
    # 测试场景加载和验证
    for scenario_id in available_scenarios[:2]:  # 只测试前两个
        try:
            loader = ScenarioLoader()
            data = loader.load_scenario(scenario_id)
            
            # 测试几个不同的断点位置
            test_breakpoints = [1, len(data["history"]) // 2, len(data["history"]) - 1]
            
            for breakpoint in test_breakpoints:
                if breakpoint > 0:
                    loader.validate_breakpoint(data["history"], breakpoint)
                    print(f"✓ 场景 {scenario_id} 断点 {breakpoint} 验证通过")
            
        except Exception as e:
            print(f"❌ 场景 {scenario_id} 测试失败: {e}")
            return False
    
    return True

async def run_actual_test():
    """运行实际的断点复现测试（需要API密钥）"""
    print("\n=== 运行实际测试 ===")
    print("⚠ 这将调用实际的API，请确保有足够的配额")
    
    # 询问用户是否继续
    response = input("是否继续运行实际测试？(y/N): ").strip().lower()
    if response != 'y':
        print("跳过实际测试")
        return True
    
    try:
        reproducer = BreakpointReproducer()
        
        # 使用一个简单的测试场景
        test_scenario = "1"
        test_breakpoint = 3  # 较早的断点，减少API调用
        test_model = "gpt-4o-mini"  # 使用较便宜的模型
        
        print(f"开始测试: 场景 {test_scenario}, 断点 {test_breakpoint}, 模型 {test_model}")
        
        output_path = await reproducer.run_reproduction(
            test_scenario, test_breakpoint, test_model
        )
        
        print(f"✅ 实际测试成功！结果保存到: {output_path}")
        
        # 验证输出文件
        if output_path.exists() and output_path.stat().st_size > 0:
            print("✓ 输出文件验证通过")
            return True
        else:
            print("❌ 输出文件验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 实际测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 === 专注断点复现功能测试 ===\n")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("配置验证", test_config_validation),
        ("场景加载", test_scenario_loading),
        ("干运行测试", test_dry_run),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = await test_func()
        test_results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    # 汇总测试结果
    print(f"\n{'='*50}")
    print("📊 === 测试结果汇总 ===")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有基础测试都通过了！")
        
        # 询问是否运行实际测试
        if Config.API_KEY:
            await run_actual_test()
        else:
            print("⚠ 未设置 API 密钥，跳过实际测试")
    else:
        print("⚠ 部分测试失败，请检查配置和环境")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

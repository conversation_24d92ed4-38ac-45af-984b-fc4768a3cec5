#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
focused_breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
专注于断点复现功能的核心实现

主要功能：
1. 从指定断点位置恢复 MagenticOne 系统状态
2. 继续执行并观察后续行为
3. 详细记录每个步骤的执行过程

使用方法：
    python focused_breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o

作者：Multi-Agent 系统调试工具
日期：2025-07-28
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone

# AutoGen 核心组件导入
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# ========================== 全局配置 ==========================
class Config:
    """配置管理类 - 集中管理所有配置参数"""
    
    # 数据路径配置
    DATA_DIR = Path("Agents_Failure_Attribution/Who&When/Hand-Crafted")
    OUTPUT_DIR = Path("logs/breakpoint_reproduction")
    
    # API 配置
    API_KEY = os.getenv("OPENAI_API_KEY")
    API_BASE = os.getenv("OPENAI_API_BASE")
    
    # 执行配置
    DEFAULT_MODEL = "gpt-4o"
    MAX_TOKENS = 512
    TEMPERATURE = 0
    
    @classmethod
    def validate(cls):
        """验证配置的有效性"""
        if not cls.API_KEY:
            raise ValueError("❌ 请设置 OPENAI_API_KEY 环境变量")
        
        if not cls.DATA_DIR.exists():
            raise FileNotFoundError(f"❌ 数据目录不存在: {cls.DATA_DIR}")
        
        # 确保输出目录存在
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓ 配置验证通过，输出目录: {cls.OUTPUT_DIR}")

# ========================== 数据加载模块 ==========================
class ScenarioLoader:
    """场景数据加载器 - 负责加载和验证失败场景数据"""
    
    @staticmethod
    def load_scenario(scenario_id: str) -> Dict[str, Any]:
        """
        加载指定场景的完整数据
        
        Args:
            scenario_id: 场景标识符（如 "1", "2" 等）
            
        Returns:
            包含以下字段的字典：
            - history: 消息历史记录列表
            - question: 原始用户问题
            - mistake_step: 错误发生的步骤编号
            - mistake_reason: 错误原因描述
            - mistake_agent: 出错的智能体名称
            
        Raises:
            FileNotFoundError: 场景文件不存在
            json.JSONDecodeError: JSON 格式错误
        """
        scenario_path = Config.DATA_DIR / f"{scenario_id}.json"
        
        if not scenario_path.exists():
            raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")
        
        try:
            with scenario_path.open(encoding="utf-8") as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"❌ JSON 格式错误: {e}")
        
        # 验证必要字段
        required_fields = ["history", "question"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"❌ 缺少必要字段: {field}")
        
        # 打印场景信息
        print(f"\n=== 场景数据加载成功 ===")
        print(f"场景ID: {scenario_id}")
        print(f"原始问题: {data['question'][:100]}...")
        print(f"历史步数: {len(data['history'])}")
        print(f"错误步骤: {data.get('mistake_step', '未标注')}")
        print(f"错误原因: {data.get('mistake_reason', '未标注')}")
        print(f"出错智能体: {data.get('mistake_agent', '未标注')}")
        
        return data
    
    @staticmethod
    def validate_breakpoint(history: List[Dict], breakpoint: int) -> None:
        """
        验证断点位置的有效性
        
        Args:
            history: 消息历史记录
            breakpoint: 断点位置（1-based）
            
        Raises:
            ValueError: 断点位置无效
        """
        total_steps = len(history)
        
        if breakpoint <= 0:
            raise ValueError(f"❌ 断点位置必须大于0，当前值: {breakpoint}")
        
        if breakpoint > total_steps:
            raise ValueError(f"❌ 断点位置超出范围，最大值: {total_steps}，当前值: {breakpoint}")
        
        print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

# ========================== 系统状态恢复模块 ==========================
class StateRestorer:
    """系统状态恢复器 - 负责将历史消息注入到 MagenticOne 系统中"""
    
    def __init__(self, agent: MagenticOne):
        """
        初始化状态恢复器
        
        Args:
            agent: MagenticOne 实例
        """
        self.agent = agent
        self.injection_stats = {
            "total_attempted": 0,
            "successful": 0,
            "failed": 0,
            "failed_messages": []
        }
    
    async def inject_single_message(self, message_data: Dict[str, Any], step_num: int) -> bool:
        """
        注入单条历史消息到系统运行时
        
        Args:
            message_data: 消息数据，包含 role 和 content
            step_num: 步骤编号（用于日志）
            
        Returns:
            注入是否成功
        """
        try:
            role = message_data.get("role", "assistant")
            content = message_data.get("content", "")
            
            # 创建 TextMessage 对象
            message = TextMessage(source=role, content=content)
            
            # 尝试多种注入方式（按优先级排序）
            injection_methods = [
                ("_runtime.publish", lambda: self.agent._runtime.publish(message)),
                ("runtime.publish", lambda: self.agent.runtime.publish(message)),
                ("send", lambda: self.agent.send(message))
            ]
            
            for method_name, method_func in injection_methods:
                try:
                    if hasattr(self.agent, method_name.split('.')[0]):
                        await method_func()
                        print(f"  ✓ [步骤 {step_num:2d}] 使用 {method_name} 注入成功")
                        return True
                except Exception as e:
                    print(f"  ⚠ [步骤 {step_num:2d}] {method_name} 方法失败: {e}")
                    continue
            
            # 所有方法都失败
            print(f"  ❌ [步骤 {step_num:2d}] 所有注入方法都失败")
            return False
            
        except Exception as e:
            print(f"  ❌ [步骤 {step_num:2d}] 消息注入异常: {e}")
            return False
    
    async def restore_to_breakpoint(self, history: List[Dict[str, Any]], breakpoint: int) -> Dict[str, int]:
        """
        恢复系统状态到指定断点位置
        
        Args:
            history: 完整的消息历史记录
            breakpoint: 断点位置（1-based）
            
        Returns:
            包含注入统计信息的字典
        """
        print(f"\n=== 开始状态恢复到断点 {breakpoint} ===")
        
        # 重置统计信息
        self.injection_stats = {
            "total_attempted": 0,
            "successful": 0,
            "failed": 0,
            "failed_messages": []
        }
        
        # 逐条注入断点之前的消息
        for i, message_data in enumerate(history[:breakpoint]):
            step_num = i + 1
            role = message_data.get("role", "unknown")
            content_preview = str(message_data.get("content", ""))[:80].replace('\n', ' ')
            
            print(f"[步骤 {step_num:2d}] 注入消息 [{role}]: {content_preview}...")
            
            self.injection_stats["total_attempted"] += 1
            success = await self.inject_single_message(message_data, step_num)
            
            if success:
                self.injection_stats["successful"] += 1
            else:
                self.injection_stats["failed"] += 1
                self.injection_stats["failed_messages"].append({
                    "step": step_num,
                    "role": role,
                    "content_preview": content_preview
                })
        
        # 打印恢复结果统计
        print(f"\n=== 状态恢复完成 ===")
        print(f"总计尝试: {self.injection_stats['total_attempted']} 条消息")
        print(f"成功注入: {self.injection_stats['successful']} 条消息")
        print(f"注入失败: {self.injection_stats['failed']} 条消息")
        
        if self.injection_stats["failed"] > 0:
            print(f"⚠ 失败消息详情:")
            for failed_msg in self.injection_stats["failed_messages"]:
                print(f"  - 步骤 {failed_msg['step']}: [{failed_msg['role']}] {failed_msg['content_preview']}")
        
        return self.injection_stats

# ========================== 断点继续执行模块 ==========================
class BreakpointExecutor:
    """断点执行器 - 负责从断点位置继续执行系统推理"""
    
    def __init__(self, agent: MagenticOne):
        """
        初始化断点执行器
        
        Args:
            agent: 已恢复状态的 MagenticOne 实例
        """
        self.agent = agent
        self.execution_log = []
    
    async def continue_from_breakpoint(self, original_question: str, breakpoint_step: int) -> List[Dict[str, Any]]:
        """
        从断点位置继续执行系统推理
        
        Args:
            original_question: 原始用户问题
            breakpoint_step: 断点步骤编号
            
        Returns:
            从断点开始的新消息历史记录列表
        """
        print(f"\n=== 从断点 {breakpoint_step} 继续执行 ===")
        print(f"原始问题: {original_question}")
        print(f"开始继续推理...\n")
        
        continuation_history = []
        current_step = breakpoint_step
        
        try:
            # 使用 run_stream 从当前状态继续执行
            async for message in self.agent.run_stream(task=original_question):
                current_step += 1
                
                # 提取消息属性
                role = self._extract_role(message)
                content = self._extract_content(message)
                timestamp = self._extract_timestamp(message)
                
                # 控制台输出（带截断）
                content_preview = str(content)[:120].replace('\n', ' ')
                print(f"[步骤 {current_step:2d}] [{role}] {content_preview}...")
                
                # 记录到历史
                message_record = {
                    "step": current_step,
                    "role": role,
                    "content": str(content),
                    "timestamp": timestamp,
                    "message_type": getattr(message, "type", "unknown")
                }
                continuation_history.append(message_record)
                
                # 检查是否为任务结果（结束条件）
                if hasattr(message, "type") and message.type == "task_result":
                    print(f"\n✓ 检测到任务结果，执行完成")
                    break
                    
        except Exception as e:
            print(f"\n❌ 执行过程中出现异常: {e}")
            print(f"已执行步数: {len(continuation_history)}")
            # 记录异常信息
            continuation_history.append({
                "step": current_step + 1,
                "role": "system_error",
                "content": f"执行异常: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message_type": "error"
            })
        
        print(f"\n=== 断点继续执行完成 ===")
        print(f"新增执行步数: {len(continuation_history)}")
        print(f"总执行步数: {breakpoint_step + len(continuation_history)}")
        
        return continuation_history
    
    def _extract_role(self, message) -> str:
        """提取消息角色"""
        return getattr(message, "role", None) or getattr(message, "source", "assistant")
    
    def _extract_content(self, message) -> str:
        """提取消息内容"""
        return str(getattr(message, "content", ""))
    
    def _extract_timestamp(self, message) -> str:
        """提取消息时间戳"""
        timestamp = getattr(message, "created_at", datetime.now(timezone.utc))
        if hasattr(timestamp, 'isoformat'):
            return timestamp.isoformat()
        return str(timestamp)

# ========================== 结果保存模块 ==========================
class ResultSaver:
    """结果保存器 - 负责保存断点复现的完整结果"""

    @staticmethod
    def save_reproduction_result(
        scenario_id: str,
        breakpoint: int,
        model: str,
        original_data: Dict[str, Any],
        continuation_history: List[Dict[str, Any]],
        injection_stats: Dict[str, int]
    ) -> Path:
        """
        保存断点复现的完整结果到 JSON 文件

        Args:
            scenario_id: 场景标识符
            breakpoint: 断点位置
            model: 使用的模型名称
            original_data: 原始场景数据
            continuation_history: 从断点继续的历史记录
            injection_stats: 消息注入统计信息

        Returns:
            保存文件的路径
        """
        # 构建完整的执行历史（原始历史 + 断点继续历史）
        full_history = original_data["history"][:breakpoint] + continuation_history

        # 构建输出数据结构
        output_data = {
            # 基本信息
            "scenario_id": scenario_id,
            "reproduction_method": "focused_breakpoint_continuation",
            "breakpoint_step": breakpoint,
            "model": model,
            "reproduced_at": datetime.now(timezone.utc).isoformat(),

            # 原始任务信息
            "original_task": {
                "question": original_data["question"],
                "ground_truth": original_data.get("ground_truth", ""),
                "total_original_steps": len(original_data["history"])
            },

            # 原始失败信息
            "original_failure": {
                "mistake_step": original_data.get("mistake_step"),
                "mistake_reason": original_data.get("mistake_reason"),
                "mistake_agent": original_data.get("mistake_agent")
            },

            # 复现执行统计
            "reproduction_stats": {
                "breakpoint_position": breakpoint,
                "continuation_steps": len(continuation_history),
                "total_reproduced_steps": len(full_history),
                "injection_stats": injection_stats
            },

            # 完整历史记录
            "execution_history": {
                "full_history": full_history,
                "original_history": original_data["history"][:breakpoint],
                "continuation_only": continuation_history
            },

            # 复现配置
            "reproduction_config": {
                "inject_up_to_step": breakpoint,
                "continue_from_step": breakpoint + 1,
                "model_config": {
                    "model": model,
                    "temperature": Config.TEMPERATURE,
                    "max_tokens": Config.MAX_TOKENS
                }
            }
        }

        # 生成输出文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"scenario_{scenario_id}_breakpoint_{breakpoint}_{model}_{timestamp}.json"
        output_path = Config.OUTPUT_DIR / output_filename

        # 保存到文件
        with output_path.open("w", encoding="utf-8") as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        # 打印保存结果
        print(f"\n=== 复现结果已保存 ===")
        print(f"保存路径: {output_path}")
        print(f"文件大小: {output_path.stat().st_size / 1024:.1f} KB")
        print(f"数据统计:")
        print(f"  - 原始步数: {len(original_data['history'])}")
        print(f"  - 断点位置: {breakpoint}")
        print(f"  - 继续步数: {len(continuation_history)}")
        print(f"  - 总计步数: {len(full_history)}")
        print(f"  - 注入成功率: {injection_stats['successful']}/{injection_stats['total_attempted']}")

        return output_path

# ========================== 主执行流程 ==========================
class BreakpointReproducer:
    """断点复现器 - 协调整个断点复现流程的主控制器"""

    def __init__(self):
        """初始化断点复现器"""
        self.config = Config
        self.scenario_loader = ScenarioLoader()
        self.state_restorer = None
        self.breakpoint_executor = None
        self.result_saver = ResultSaver()

    async def run_reproduction(self, scenario_id: str, breakpoint: int, model: str) -> Path:
        """
        执行完整的断点复现流程

        Args:
            scenario_id: 场景标识符
            breakpoint: 断点位置
            model: 使用的模型名称

        Returns:
            保存结果文件的路径
        """
        print(f"🚀 === 断点复现任务开始 ===")
        print(f"场景ID: {scenario_id}")
        print(f"断点位置: 第 {breakpoint} 步")
        print(f"使用模型: {model}")

        # 步骤1: 验证配置
        self.config.validate()

        # 步骤2: 加载场景数据
        scenario_data = self.scenario_loader.load_scenario(scenario_id)

        # 步骤3: 验证断点位置
        self.scenario_loader.validate_breakpoint(scenario_data["history"], breakpoint)

        # 步骤4: 初始化 MagenticOne 系统
        agent = await self._initialize_magentic_one(model)

        # 步骤5: 恢复系统状态到断点
        self.state_restorer = StateRestorer(agent)
        injection_stats = await self.state_restorer.restore_to_breakpoint(
            scenario_data["history"], breakpoint
        )

        # 步骤6: 从断点继续执行
        self.breakpoint_executor = BreakpointExecutor(agent)
        continuation_history = await self.breakpoint_executor.continue_from_breakpoint(
            scenario_data["question"], breakpoint
        )

        # 步骤7: 保存复现结果
        output_path = self.result_saver.save_reproduction_result(
            scenario_id, breakpoint, model, scenario_data, continuation_history, injection_stats
        )

        print(f"\n🎉 === 断点复现任务完成 ===")
        return output_path

    async def _initialize_magentic_one(self, model: str) -> MagenticOne:
        """
        初始化 MagenticOne 系统

        Args:
            model: 模型名称

        Returns:
            配置好的 MagenticOne 实例
        """
        print(f"\n=== 初始化 MagenticOne 系统 ===")

        # 创建模型客户端
        client = OpenAIChatCompletionClient(
            model=model,
            api_key=self.config.API_KEY,
            base_url=self.config.API_BASE or None,
            temperature=self.config.TEMPERATURE,
            model_extras={"max_tokens": self.config.MAX_TOKENS}
        )
        print(f"✓ 模型客户端创建成功: {model}")

        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()
        print(f"✓ 代码执行器创建成功")

        # 创建 MagenticOne 实例
        agent = MagenticOne(client=client, code_executor=code_executor)
        print(f"✓ MagenticOne 系统初始化完成")

        return agent

# ========================== 命令行接口 ==========================
async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(
        description="专注的断点复现工具 - 从指定断点位置恢复并继续执行 MagenticOne",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python focused_breakpoint_reproduction.py --scenario 1 --breakpoint 12
  python focused_breakpoint_reproduction.py --scenario 5 --breakpoint 8 --model gpt-4o-mini

注意事项:
  - 确保设置了 OPENAI_API_KEY 环境变量
  - 断点位置从1开始计数
  - 结果将保存到 logs/breakpoint_reproduction/ 目录
        """
    )

    parser.add_argument(
        "--scenario",
        required=True,
        help="场景ID（如 1, 2, 3 等）"
    )
    parser.add_argument(
        "--breakpoint",
        type=int,
        required=True,
        help="断点位置（步骤编号，从1开始）"
    )
    parser.add_argument(
        "--model",
        default=Config.DEFAULT_MODEL,
        help=f"使用的模型（默认: {Config.DEFAULT_MODEL}）"
    )

    args = parser.parse_args()

    try:
        # 创建断点复现器并执行
        reproducer = BreakpointReproducer()
        output_path = await reproducer.run_reproduction(
            args.scenario, args.breakpoint, args.model
        )

        print(f"\n✅ 执行成功！结果已保存到: {output_path}")

    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

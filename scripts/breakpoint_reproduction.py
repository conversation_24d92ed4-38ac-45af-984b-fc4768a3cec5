#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~
简化版断点恢复系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统

核心功能：
1. 从错误步骤开始往前恢复执行
2. 支持多种模型（ollama、openai、cloudgpt）
3. 简单的消息注入和继续执行
4. 保存复现结果

Usage:
    python breakpoint_reproduction.py --scenario 1                                    # 自动读取错误步骤，默认使用 ollama llama3.1
    python breakpoint_reproduction.py --scenario 1 --endpoint openai --model gpt-4o  # 自动读取错误步骤，使用 OpenAI
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12                   # 手动指定断点位置
    python breakpoint_reproduction.py --scenario 1 --endpoint cloudgpt --model gpt-4o-2024-05-13
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any, Dict, List
from datetime import datetime, timezone

# AutoGen imports
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# ========================== 配置和路径 ==========================

# API 配置
API_KEY = os.getenv("OPENAI_API_KEY")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
OLLAMA_HOST = "http://localhost:11434"

# 路径配置
SCRIPT_DIR = Path(__file__).parent.parent  # 获取项目根目录
DATA_DIR = SCRIPT_DIR / "Agents_Failure_Attribution/Who&When/Hand-Crafted"
OUTPUT_DIR = SCRIPT_DIR / "logs/breakpoint_reproduction"

# ========================== 模型客户端创建 ==========================

def get_client(endpoint_type: str = "ollama", model: str = None):
    """获取适当的模型客户端"""
    if endpoint_type == "ollama":
        return OllamaChatCompletionClient(
            model=model or "llama3.1",
            host=OLLAMA_HOST,
        )
    elif endpoint_type == "openai":
        if not API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable must be set for OpenAI usage")
        return OpenAIChatCompletionClient(
            model=model or "gpt-4o",
            api_key=API_KEY,
            base_url=API_BASE,
        )
    elif endpoint_type == "cloudgpt":
        if not model:
            raise ValueError("Model name must be specified for cloudgpt")
        return AzureOpenAIChatCompletionClient(
            model=model,
        )
    else:
        raise ValueError(f"Unknown endpoint type: {endpoint_type}")

# ========================== 消息注入工具 ==========================

async def inject_message(agent: MagenticOne, message_data: Dict[str, Any]) -> bool:
    """将单条历史消息注入到 MagenticOne 运行时"""
    try:
        role = message_data.get("role", "assistant")
        content = message_data.get("content", "")
        
        # 创建 TextMessage 对象
        message = TextMessage(source=role, content=content)
        
        # 尝试多种注入方式
        if hasattr(agent, "_runtime") and hasattr(agent._runtime, "publish"):
            await agent._runtime.publish(message)
        elif hasattr(agent, "runtime") and hasattr(agent.runtime, "publish"):
            await agent.runtime.publish(message)
        elif hasattr(agent, "send"):
            await agent.send(message)
        else:
            print(f"[警告] 无法找到消息注入接口，跳过消息: [{role}]")
            return False
            
        return True
        
    except Exception as e:
        print(f"[错误] 消息注入失败: {e}")
        return False

# ========================== 场景数据加载 ==========================

def load_scenario(scenario_id: str) -> Dict[str, Any]:
    """加载场景数据"""
    scenario_path = DATA_DIR / f"{scenario_id}.json"
    
    if not scenario_path.exists():
        raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")
    
    with scenario_path.open(encoding="utf-8") as f:
        data = json.load(f)
    
    print(f"\n=== 场景数据加载成功 ===")
    print(f"场景ID: {scenario_id}")
    print(f"原始问题: {data['question'][:100]}...")
    print(f"历史步数: {len(data['history'])}")
    print(f"错误步骤: {data.get('mistake_step', '未标注')}")
    print(f"错误原因: {data.get('mistake_reason', '未标注')}")
    print(f"出错智能体: {data.get('mistake_agent', '未标注')}")
    
    return data

# ========================== 断点复现主要功能 ==========================

async def restore_from_breakpoint(scenario_id: str, breakpoint: int = None, endpoint_type: str = "ollama", model: str = None):
    """从指定断点位置恢复并继续执行"""

    print(f"\n🚀 === 断点复现开始 ===")
    print(f"场景ID: {scenario_id}")
    print(f"断点位置: 第 {breakpoint} 步")
    print(f"端点类型: {endpoint_type}")
    print(f"模型: {model or ('llama3.1' if endpoint_type == 'ollama' else 'default')}")

    # 1. 加载场景数据
    scenario_data = load_scenario(scenario_id)

    # 2. 自动获取或验证断点位置
    if breakpoint is None:
        # 从场景数据中自动读取错误步骤
        mistake_step = scenario_data.get("mistake_step")
        if mistake_step:
            # mistake_step 可能是字符串，需要转换为整数
            breakpoint = int(mistake_step)
            print(f"✓ 自动从场景数据读取断点位置: 第 {breakpoint} 步")
            print(f"  错误原因: {scenario_data.get('mistake_reason', '未知')}")
            print(f"  出错智能体: {scenario_data.get('mistake_agent', '未知')}")
        else:
            raise ValueError(f"❌ 场景数据中未找到 mistake_step 字段，请手动指定断点位置")

    # 验证断点位置
    total_steps = len(scenario_data["history"])
    if breakpoint <= 0 or breakpoint > total_steps:
        raise ValueError(f"❌ 断点位置无效: {breakpoint}，有效范围: 1-{total_steps}")

    print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

    # 3. 初始化 MagenticOne 系统
    print(f"\n=== 初始化 MagenticOne 系统 ===")
    client = get_client(endpoint_type, model)
    code_executor = LocalCommandLineCodeExecutor()
    agent = MagenticOne(client=client, code_executor=code_executor)
    print(f"✓ MagenticOne 系统初始化完成")

    # 4. 构建上下文提示（而不是逐条注入消息）
    print(f"\n=== 构建上下文提示到第 {breakpoint} 步 ===")

    # 构建完整的对话历史作为上下文
    context_messages = []
    for i, message_data in enumerate(scenario_data["history"][:breakpoint]):
        step_num = i + 1
        role = message_data.get("role", "unknown")
        content = message_data.get("content", "")
        content_preview = str(content)[:60].replace('\n', ' ')

        print(f"[步骤 {step_num:2d}] 添加上下文 [{role}]: {content_preview}...")
        context_messages.append(f"[{role}]: {content}")

    # 构建包含历史上下文的新任务描述
    original_question = scenario_data["question"]
    context_prompt = "\n\n".join(context_messages)

    enhanced_task = f"""
基于以下对话历史，请继续完成任务：

原始任务：{original_question}

对话历史：
{context_prompt}

请从上述对话历史的状态继续执行，完成原始任务。
"""

    print(f"\n上下文构建完成:")
    print(f"  ✓ 历史消息数: {len(context_messages)} 条")
    print(f"  → 将使用增强的任务描述继续执行")

    # 5. 从断点继续执行
    print(f"\n=== 从断点继续执行 ===")
    continuation_history = []
    step_counter = breakpoint

    try:
        # 使用增强的任务描述（包含历史上下文）继续执行
        async for message in agent.run_stream(task=enhanced_task):
            step_counter += 1

            # 提取消息信息
            role = getattr(message, "role", None) or getattr(message, "source", "assistant")
            content = getattr(message, "content", "")
            timestamp = getattr(message, "created_at", datetime.now(timezone.utc))

            # 控制台输出（截断显示）
            content_preview = str(content)[:100].replace('\n', ' ')
            print(f"[步骤 {step_counter:2d}] [{role}] {content_preview}...")

            # 记录到历史
            message_record = {
                "step": step_counter,
                "role": role,
                "content": str(content),
                "timestamp": timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp)
            }
            continuation_history.append(message_record)

            # 检查是否为任务结果
            if hasattr(message, "type") and message.type == "task_result":
                print(f"\n✓ 检测到任务结果，停止执行")
                break

    except Exception as e:
        print(f"\n[错误] 执行过程中出现异常: {e}")
        print(f"已执行步数: {len(continuation_history)}")

    print(f"\n断点继续执行完成，新增 {len(continuation_history)} 步")

    return scenario_data, continuation_history, len(context_messages), 0

# ========================== 结果保存 ==========================

def save_reproduction_results(scenario_id: str, breakpoint: int, endpoint_type: str, model: str,
                            scenario_data: Dict[str, Any], continuation_history: List[Dict[str, Any]],
                            context_messages_count: int, unused_param: int) -> Path:
    """保存断点复现结果"""

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # 构建完整的执行历史（原始历史 + 断点继续历史）
    full_history = scenario_data["history"][:breakpoint] + continuation_history

    # 构建输出数据
    output_data = {
        "scenario_id": scenario_id,
        "reproduction_method": "simple_breakpoint_continuation",
        "breakpoint_step": breakpoint,
        "endpoint_type": endpoint_type,
        "model": model or ("llama3.1" if endpoint_type == "ollama" else "gpt-4o"),
        "original_question": scenario_data["question"],
        "ground_truth": scenario_data.get("ground_truth", ""),

        # 原始失败信息
        "original_failure": {
            "mistake_step": scenario_data.get("mistake_step"),
            "mistake_reason": scenario_data.get("mistake_reason"),
            "mistake_agent": scenario_data.get("mistake_agent")
        },

        # 执行统计
        "execution_stats": {
            "original_steps": len(scenario_data["history"]),
            "breakpoint_position": breakpoint,
            "continuation_steps": len(continuation_history),
            "total_reproduced_steps": len(full_history),
            "context_messages_count": context_messages_count,
            "failed_injections": 0,
            "context_success_rate": 1.0
        },

        # 完整历史记录
        "full_history": full_history,
        "continuation_only": continuation_history,

        # 元数据
        "reproduced_at": datetime.now(timezone.utc).isoformat(),
        "reproduction_config": {
            "inject_up_to_step": breakpoint,
            "continue_from_step": breakpoint + 1
        }
    }

    # 保存文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_name = model or ("llama3.1" if endpoint_type == "ollama" else "gpt-4o")
    output_filename = f"reproduction_scenario_{scenario_id}_bp_{breakpoint}_{endpoint_type}_{model_name}_{timestamp}.json"
    output_path = OUTPUT_DIR / output_filename

    with output_path.open("w", encoding="utf-8") as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"\n✓ 断点复现结果已保存到: {output_path}")
    print(f"  - 原始步数: {len(scenario_data['history'])}")
    print(f"  - 断点位置: {breakpoint}")
    print(f"  - 继续步数: {len(continuation_history)}")
    print(f"  - 总计步数: {len(full_history)}")
    print(f"  - 上下文消息数: {context_messages_count}")

    return output_path

# ========================== 命令行接口 ==========================

async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(
        description="简化版断点复现系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 自动读取错误步骤，默认使用 ollama llama3.1
  python breakpoint_reproduction.py --scenario 1

  # 自动读取错误步骤，使用 OpenAI
  python breakpoint_reproduction.py --scenario 1 --endpoint openai --model gpt-4o

  # 手动指定断点位置
  python breakpoint_reproduction.py --scenario 1 --breakpoint 12

  # 使用 CloudGPT
  python breakpoint_reproduction.py --scenario 1 --endpoint cloudgpt --model gpt-4o-2024-05-13
        """
    )

    parser.add_argument(
        "--scenario",
        required=True,
        help="场景ID（如 1, 2, 3 等）"
    )

    parser.add_argument(
        "--breakpoint",
        type=int,
        help="断点位置（步骤编号），如果不指定则自动从场景数据中读取 mistake_step"
    )

    parser.add_argument(
        "--endpoint",
        choices=["ollama", "openai", "cloudgpt"],
        default="ollama",
        help="端点类型（默认: ollama）"
    )

    parser.add_argument(
        "--model",
        help="模型名称（ollama默认: llama3.1, openai默认: gpt-4o）"
    )

    args = parser.parse_args()

    try:
        print(f"🚀 开始断点复现任务...")

        # 执行断点复现
        scenario_data, continuation_history, context_messages_count, _ = await restore_from_breakpoint(
            args.scenario, args.breakpoint, args.endpoint, args.model
        )

        # 获取实际使用的断点位置（可能是自动读取的）
        actual_breakpoint = args.breakpoint
        if actual_breakpoint is None:
            mistake_step = scenario_data.get("mistake_step")
            if mistake_step:
                actual_breakpoint = int(mistake_step)

        # 保存结果
        result_path = save_reproduction_results(
            args.scenario, actual_breakpoint, args.endpoint, args.model,
            scenario_data, continuation_history, context_messages_count, 0
        )

        # 打印总结
        print(f"\n" + "="*60)
        print(f"断点复现执行摘要")
        print(f"="*60)
        print(f"场景ID: {args.scenario}")
        print(f"断点位置: 第 {actual_breakpoint} 步" + (" (自动读取)" if args.breakpoint is None else " (手动指定)"))
        print(f"端点类型: {args.endpoint}")
        print(f"模型: {args.model or ('llama3.1' if args.endpoint == 'ollama' else 'default')}")
        print(f"继续执行步数: {len(continuation_history)}")
        print(f"上下文消息数: {context_messages_count}")
        print(f"结果文件: {result_path}")
        print(f"="*60)

        print(f"\n✅ 断点复现任务完成！")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~
增强版断点恢复系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统

核心功能：
1. 多层次状态恢复（消息历史、Agent状态、环境状态）
2. 详细的执行监控和对比分析
3. 智能断点选择和状态验证
4. 完整的日志数据整合
5. 实验结果的结构化保存

主要改进：
- 模块化设计，便于维护和扩展
- 多数据源整合（JSON + 详细日志）
- 精确的状态恢复和验证机制
- 实时执行监控和差异分析
- 智能断点推荐系统

Usage:
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o
    python breakpoint_reproduction.py --scenario 1 --breakpoint auto --model gpt-4o  # 自动选择最佳断点
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
import logging

# AutoGen imports
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# ========================== 数据结构定义 ==========================

@dataclass
class ExecutionStep:
    """单个执行步骤的完整信息"""
    step_number: int
    agent_name: str
    action_type: str
    content: str
    timestamp: str
    success: bool
    error_info: Optional[str] = None
    state_snapshot: Optional[Dict[str, Any]] = None

@dataclass
class StateSnapshot:
    """系统状态快照"""
    step_number: int
    message_history: List[Dict[str, Any]]
    agent_states: Dict[str, Any]
    environment_state: Dict[str, Any]
    runtime_state: Dict[str, Any]
    timestamp: str

@dataclass
class BreakpointAnalysis:
    """断点分析结果"""
    recommended_breakpoints: List[int]
    critical_decision_points: List[int]
    state_transition_points: List[int]
    error_propagation_chain: List[int]
    analysis_summary: str

# ========================== 全局配置类 ==========================

class Config:
    """增强版配置管理类 - 集中管理所有配置参数"""

    # 数据路径配置
    DATA_DIR = Path("Agents_Failure_Attribution/Who&When/Hand-Crafted")
    LOGS_DIR = Path("logs/generated")
    OUTPUT_DIR = Path("logs/breakpoint_reproduction")

    # API 配置
    API_KEY = os.getenv("OPENAI_API_KEY")
    API_BASE = os.getenv("OPENAI_API_BASE")

    # 执行配置
    DEFAULT_MODEL = "gpt-4o"
    MAX_TOKENS = 512
    TEMPERATURE = 0

    # 监控配置
    ENABLE_DETAILED_MONITORING = True
    SAVE_STATE_SNAPSHOTS = True
    COMPARE_WITH_ORIGINAL = True

    # 日志配置
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    @classmethod
    def validate(cls):
        """验证配置的有效性"""
        if not cls.API_KEY:
            raise ValueError("❌ 请设置 OPENAI_API_KEY 环境变量")

        if not cls.DATA_DIR.exists():
            raise FileNotFoundError(f"❌ 数据目录不存在: {cls.DATA_DIR}")

        if not cls.LOGS_DIR.exists():
            raise FileNotFoundError(f"❌ 日志目录不存在: {cls.LOGS_DIR}")

        # 确保输出目录存在
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓ 配置验证通过，输出目录: {cls.OUTPUT_DIR}")

# ========================== 日志数据整合模块 ==========================

class LogDataIntegrator:
    """日志数据整合器 - 整合多个日志文件构建完整执行轨迹"""

    def __init__(self, logs_dir: Path):
        self.logs_dir = logs_dir
        self.logger = logging.getLogger(__name__)

    def integrate_scenario_logs(self, scenario_id: str, model: str) -> Dict[str, Any]:
        """
        整合指定场景的所有日志数据

        Args:
            scenario_id: 场景标识符
            model: 模型名称

        Returns:
            整合后的完整日志数据
        """
        self.logger.info(f"开始整合场景 {scenario_id} 的日志数据")

        # 构建日志文件路径
        log_files = self._find_log_files(scenario_id, model)

        integrated_data = {
            "scenario_id": scenario_id,
            "model": model,
            "main_execution": {},
            "llm_calls": [],
            "detailed_logs": [],
            "execution_timeline": [],
            "state_snapshots": {}
        }

        # 加载主执行日志
        if log_files.get("main"):
            integrated_data["main_execution"] = self._load_json_file(log_files["main"])

        # 加载LLM调用日志
        if log_files.get("llm_calls"):
            integrated_data["llm_calls"] = self._load_json_file(log_files["llm_calls"])

        # 加载详细日志
        if log_files.get("detailed"):
            integrated_data["detailed_logs"] = self._load_json_file(log_files["detailed"])

        # 构建执行时间线
        integrated_data["execution_timeline"] = self._build_execution_timeline(integrated_data)

        # 构建状态快照
        integrated_data["state_snapshots"] = self._build_state_snapshots(integrated_data)

        self.logger.info(f"日志整合完成，共 {len(integrated_data['execution_timeline'])} 个执行步骤")
        return integrated_data

    def _find_log_files(self, scenario_id: str, model: str) -> Dict[str, Path]:
        """查找相关的日志文件"""
        log_files = {}

        # 查找主执行日志
        main_patterns = [
            f"scenario_{scenario_id}_*_{model}.json",
            f"scenario_{scenario_id}_{model}.json"
        ]

        for pattern in main_patterns:
            matches = list(self.logs_dir.glob(pattern))
            if matches:
                log_files["main"] = matches[0]
                break

        # 查找LLM调用日志
        llm_patterns = [
            f"scenario_{scenario_id}_*_{model}_llm_calls_formatted.json",
            f"scenario_{scenario_id}_{model}_llm_calls_formatted.json"
        ]

        for pattern in llm_patterns:
            matches = list(self.logs_dir.glob(pattern))
            if matches:
                log_files["llm_calls"] = matches[0]
                break

        # 查找详细日志
        detailed_patterns = [
            f"scenario_{scenario_id}_*_{model}_log_formatted.json",
            f"scenario_{scenario_id}_{model}_log_formatted.json"
        ]

        for pattern in detailed_patterns:
            matches = list(self.logs_dir.glob(pattern))
            if matches:
                log_files["detailed"] = matches[0]
                break

        self.logger.info(f"找到日志文件: {list(log_files.keys())}")
        return log_files

    def _load_json_file(self, file_path: Path) -> Any:
        """安全加载JSON文件"""
        try:
            with file_path.open(encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载文件失败 {file_path}: {e}")
            return {}

    def _build_execution_timeline(self, integrated_data: Dict[str, Any]) -> List[ExecutionStep]:
        """构建完整的执行时间线"""
        timeline = []

        # 从主执行日志提取步骤
        main_history = integrated_data.get("main_execution", {}).get("history", [])
        for i, step in enumerate(main_history):
            timeline.append(ExecutionStep(
                step_number=i + 1,
                agent_name=step.get("role", "unknown"),
                action_type="message",
                content=step.get("content", ""),
                timestamp=step.get("timestamp", ""),
                success=True
            ))

        # 从详细日志补充更多信息
        detailed_logs = integrated_data.get("detailed_logs", [])
        if isinstance(detailed_logs, list):
            for log_entry in detailed_logs:
                # 根据日志条目的结构来提取信息
                # 这里需要根据实际的日志格式来调整
                pass

        return timeline

    def _build_state_snapshots(self, integrated_data: Dict[str, Any]) -> Dict[int, StateSnapshot]:
        """构建状态快照"""
        snapshots = {}

        timeline = integrated_data.get("execution_timeline", [])
        for step in timeline:
            snapshots[step.step_number] = StateSnapshot(
                step_number=step.step_number,
                message_history=integrated_data.get("main_execution", {}).get("history", [])[:step.step_number],
                agent_states={},  # 需要从详细日志中提取
                environment_state={},  # 需要从详细日志中提取
                runtime_state={},  # 需要从详细日志中提取
                timestamp=step.timestamp
            )

        return snapshots

# ========================== 智能断点分析模块 ==========================

class BreakpointAnalyzer:
    """智能断点分析器 - 分析最佳断点位置和关键决策点"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def analyze_breakpoints(self, scenario_data: Dict[str, Any],
                          integrated_logs: Dict[str, Any]) -> BreakpointAnalysis:
        """
        分析最佳断点位置

        Args:
            scenario_data: 原始场景数据
            integrated_logs: 整合后的日志数据

        Returns:
            断点分析结果
        """
        self.logger.info("开始分析最佳断点位置")

        history = scenario_data.get("history", [])
        mistake_step = scenario_data.get("mistake_step")

        # 1. 分析关键决策点
        critical_points = self._find_critical_decision_points(history)

        # 2. 分析状态转换点
        transition_points = self._find_state_transition_points(history)

        # 3. 分析错误传播链
        error_chain = self._analyze_error_propagation(history, mistake_step)

        # 4. 推荐断点位置
        recommended = self._recommend_breakpoints(
            critical_points, transition_points, error_chain, mistake_step
        )

        # 5. 生成分析摘要
        summary = self._generate_analysis_summary(
            recommended, critical_points, transition_points, error_chain
        )

        analysis = BreakpointAnalysis(
            recommended_breakpoints=recommended,
            critical_decision_points=critical_points,
            state_transition_points=transition_points,
            error_propagation_chain=error_chain,
            analysis_summary=summary
        )

        self.logger.info(f"断点分析完成，推荐 {len(recommended)} 个断点位置")
        return analysis

    def _find_critical_decision_points(self, history: List[Dict[str, Any]]) -> List[int]:
        """识别关键决策点"""
        critical_points = []

        for i, step in enumerate(history):
            content = step.get("content", "").lower()
            role = step.get("role", "")

            # 识别决策关键词
            decision_keywords = [
                "plan", "strategy", "approach", "decide", "choose",
                "next step", "should", "will", "going to"
            ]

            # 识别Orchestrator的决策
            if "orchestrator" in role.lower():
                if any(keyword in content for keyword in decision_keywords):
                    critical_points.append(i + 1)

            # 识别错误或问题
            error_keywords = ["error", "failed", "problem", "issue", "wrong"]
            if any(keyword in content for keyword in error_keywords):
                critical_points.append(i + 1)

        return critical_points

    def _find_state_transition_points(self, history: List[Dict[str, Any]]) -> List[int]:
        """识别状态转换点"""
        transition_points = []

        prev_agent = None
        for i, step in enumerate(history):
            current_agent = step.get("role", "")

            # Agent切换点
            if prev_agent and prev_agent != current_agent:
                transition_points.append(i + 1)

            prev_agent = current_agent

        return transition_points

    def _analyze_error_propagation(self, history: List[Dict[str, Any]],
                                 mistake_step: Optional[int]) -> List[int]:
        """分析错误传播链"""
        if not mistake_step:
            return []

        error_chain = []

        # 从错误步骤向前追溯
        for i in range(max(0, mistake_step - 5), mistake_step):
            error_chain.append(i + 1)

        # 包含错误步骤本身
        error_chain.append(mistake_step)

        return error_chain

    def _recommend_breakpoints(self, critical_points: List[int],
                             transition_points: List[int],
                             error_chain: List[int],
                             mistake_step: Optional[int]) -> List[int]:
        """推荐最佳断点位置"""
        candidates = set()

        # 添加关键决策点前的位置
        for point in critical_points:
            if point > 1:
                candidates.add(point - 1)

        # 添加状态转换点
        candidates.update(transition_points)

        # 添加错误传播链中的关键点
        candidates.update(error_chain)

        # 如果有明确的错误步骤，添加其前几步
        if mistake_step:
            for offset in [1, 2, 3]:
                if mistake_step - offset > 0:
                    candidates.add(mistake_step - offset)

        # 排序并返回
        return sorted(list(candidates))

    def _generate_analysis_summary(self, recommended: List[int],
                                 critical_points: List[int],
                                 transition_points: List[int],
                                 error_chain: List[int]) -> str:
        """生成分析摘要"""
        summary = f"""
断点分析摘要:
- 推荐断点位置: {recommended}
- 关键决策点: {critical_points}
- 状态转换点: {transition_points}
- 错误传播链: {error_chain}

建议优先尝试的断点:
"""

        if recommended:
            top_3 = recommended[-3:] if len(recommended) >= 3 else recommended
            for i, bp in enumerate(top_3, 1):
                summary += f"{i}. 第 {bp} 步 - "
                if bp in critical_points:
                    summary += "关键决策点"
                elif bp in transition_points:
                    summary += "状态转换点"
                elif bp in error_chain:
                    summary += "错误传播链"
                else:
                    summary += "综合推荐"
                summary += "\n"

        return summary.strip()

# ========================== 增强状态恢复模块 ==========================

class EnhancedStateRestorer:
    """增强版状态恢复器 - 多层次状态恢复和验证"""

    def __init__(self, agent: MagenticOne):
        self.agent = agent
        self.logger = logging.getLogger(__name__)
        self.injection_stats = {
            "total_messages": 0,
            "successful_injections": 0,
            "failed_injections": 0,
            "injection_errors": []
        }

    async def restore_complete_state(self, scenario_data: Dict[str, Any],
                                   integrated_logs: Dict[str, Any],
                                   breakpoint: int) -> Dict[str, Any]:
        """
        完整的多层次状态恢复

        Args:
            scenario_data: 原始场景数据
            integrated_logs: 整合后的日志数据
            breakpoint: 断点位置

        Returns:
            恢复统计信息
        """
        self.logger.info(f"开始完整状态恢复到第 {breakpoint} 步")

        # 1. 恢复消息历史
        await self._restore_message_history(scenario_data["history"], breakpoint)

        # 2. 恢复Agent状态（如果有详细状态信息）
        await self._restore_agent_states(integrated_logs, breakpoint)

        # 3. 恢复环境状态
        await self._restore_environment_state(integrated_logs, breakpoint)

        # 4. 验证状态一致性
        validation_result = await self._validate_state_consistency(breakpoint)

        # 5. 生成恢复报告
        restore_stats = {
            "breakpoint_position": breakpoint,
            "injection_stats": self.injection_stats,
            "validation_result": validation_result,
            "restore_timestamp": datetime.now(timezone.utc).isoformat()
        }

        self.logger.info(f"状态恢复完成: {self.injection_stats['successful_injections']}/{self.injection_stats['total_messages']} 消息成功注入")
        return restore_stats

    async def _restore_message_history(self, history: List[Dict[str, Any]], breakpoint: int):
        """恢复消息历史到指定断点"""
        self.logger.info(f"恢复消息历史: 前 {breakpoint} 条消息")

        for i, message_data in enumerate(history[:breakpoint]):
            step_num = i + 1
            self.injection_stats["total_messages"] += 1

            try:
                success = await self._inject_single_message(message_data, step_num)
                if success:
                    self.injection_stats["successful_injections"] += 1
                else:
                    self.injection_stats["failed_injections"] += 1

            except Exception as e:
                self.injection_stats["failed_injections"] += 1
                self.injection_stats["injection_errors"].append({
                    "step": step_num,
                    "error": str(e),
                    "message_role": message_data.get("role", "unknown")
                })
                self.logger.error(f"步骤 {step_num} 消息注入失败: {e}")

    async def _inject_single_message(self, message_data: Dict[str, Any], step_num: int) -> bool:
        """注入单条消息到运行时"""
        try:
            role = message_data.get("role", "assistant")
            content = message_data.get("content", "")

            # 创建TextMessage对象
            message = TextMessage(source=role, content=content)

            # 尝试多种注入方式
            injection_methods = [
                lambda: self._inject_via_runtime(message),
                lambda: self._inject_via_publish(message),
                lambda: self._inject_via_send(message)
            ]

            for method in injection_methods:
                try:
                    await method()
                    self.logger.debug(f"步骤 {step_num} [{role}] 消息注入成功")
                    return True
                except Exception as e:
                    self.logger.debug(f"注入方法失败: {e}")
                    continue

            self.logger.warning(f"步骤 {step_num} 所有注入方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"消息注入异常: {e}")
            return False

    async def _inject_via_runtime(self, message: TextMessage):
        """通过runtime注入消息"""
        if hasattr(self.agent, "_runtime") and hasattr(self.agent._runtime, "publish"):
            await self.agent._runtime.publish(message)
        else:
            raise AttributeError("Runtime publish method not available")

    async def _inject_via_publish(self, message: TextMessage):
        """通过publish方法注入消息"""
        if hasattr(self.agent, "runtime") and hasattr(self.agent.runtime, "publish"):
            await self.agent.runtime.publish(message)
        else:
            raise AttributeError("Publish method not available")

    async def _inject_via_send(self, message: TextMessage):
        """通过send方法注入消息"""
        if hasattr(self.agent, "send"):
            await self.agent.send(message)
        else:
            raise AttributeError("Send method not available")

    async def _restore_agent_states(self, integrated_logs: Dict[str, Any], breakpoint: int):
        """恢复各个Agent的内部状态"""
        self.logger.info("尝试恢复Agent内部状态")

        # 从详细日志中提取Agent状态信息
        state_snapshots = integrated_logs.get("state_snapshots", {})
        target_snapshot = state_snapshots.get(breakpoint)

        if target_snapshot:
            agent_states = target_snapshot.agent_states
            # 这里需要根据具体的Agent实现来恢复状态
            # 由于MagenticOne的内部状态可能不直接可访问，这部分可能需要特殊处理
            self.logger.info(f"找到第 {breakpoint} 步的Agent状态快照")
        else:
            self.logger.warning(f"未找到第 {breakpoint} 步的Agent状态信息")

    async def _restore_environment_state(self, integrated_logs: Dict[str, Any], breakpoint: int):
        """恢复环境状态（浏览器、文件系统等）"""
        self.logger.info("尝试恢复环境状态")

        # 这里可以根据需要恢复：
        # 1. 浏览器状态（如果WebSurfer有状态信息）
        # 2. 文件系统状态（如果FileSurfer有操作记录）
        # 3. 代码执行环境状态

        # 由于环境状态的复杂性，这里主要做记录和提醒
        self.logger.info("环境状态恢复需要根据具体场景手动处理")

    async def _validate_state_consistency(self, breakpoint: int) -> Dict[str, Any]:
        """验证恢复后状态的一致性"""
        self.logger.info("验证状态一致性")

        validation_result = {
            "is_consistent": True,
            "validation_checks": [],
            "warnings": [],
            "errors": []
        }

        # 检查1: 消息注入成功率
        success_rate = (self.injection_stats["successful_injections"] /
                       max(1, self.injection_stats["total_messages"]))

        validation_result["validation_checks"].append({
            "check": "message_injection_rate",
            "result": success_rate,
            "threshold": 0.8,
            "passed": success_rate >= 0.8
        })

        if success_rate < 0.8:
            validation_result["warnings"].append(
                f"消息注入成功率较低: {success_rate:.2%}"
            )

        # 检查2: 注入错误分析
        if self.injection_stats["injection_errors"]:
            validation_result["errors"].extend(self.injection_stats["injection_errors"])
            validation_result["is_consistent"] = False

        # 检查3: Agent状态检查（如果可能）
        try:
            agent_status = await self._check_agent_status()
            validation_result["validation_checks"].append({
                "check": "agent_status",
                "result": agent_status,
                "passed": agent_status.get("is_ready", False)
            })
        except Exception as e:
            validation_result["warnings"].append(f"Agent状态检查失败: {e}")

        return validation_result

    async def _check_agent_status(self) -> Dict[str, Any]:
        """检查Agent的当前状态"""
        # 这里可以检查Agent是否处于可用状态
        # 由于MagenticOne的内部实现细节，这里做基础检查
        return {
            "is_ready": True,
            "agent_type": type(self.agent).__name__,
            "has_runtime": hasattr(self.agent, "_runtime") or hasattr(self.agent, "runtime")
        }

# ========================== 执行监控模块 ==========================

class ExecutionMonitor:
    """执行监控器 - 监控断点后的执行过程并进行对比分析"""

    def __init__(self, original_history: List[Dict[str, Any]], breakpoint: int):
        self.original_history = original_history
        self.breakpoint = breakpoint
        self.new_execution_steps = []
        self.comparison_results = []
        self.logger = logging.getLogger(__name__)

    async def monitor_execution(self, agent: MagenticOne,
                              original_question: str) -> List[ExecutionStep]:
        """
        监控从断点开始的执行过程

        Args:
            agent: MagenticOne实例
            original_question: 原始问题

        Returns:
            新的执行步骤列表
        """
        self.logger.info(f"开始监控从第 {self.breakpoint + 1} 步的执行")

        step_counter = self.breakpoint

        try:
            # 使用run_stream从当前状态继续执行
            async for message in agent.run_stream(task=original_question):
                step_counter += 1

                # 提取消息信息
                role = getattr(message, "role", None) or getattr(message, "source", "assistant")
                content = getattr(message, "content", "")
                timestamp = getattr(message, "created_at", datetime.now(timezone.utc))

                # 创建执行步骤记录
                execution_step = ExecutionStep(
                    step_number=step_counter,
                    agent_name=role,
                    action_type="continuation",
                    content=str(content),
                    timestamp=timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp),
                    success=True
                )

                self.new_execution_steps.append(execution_step)

                # 实时对比分析
                await self._compare_with_original(execution_step)

                # 控制台输出
                content_preview = str(content)[:100].replace('\n', ' ')
                self.logger.info(f"[步骤 {step_counter:2d}] [{role}] {content_preview}...")

                # 检查是否为任务结果
                if hasattr(message, "type") and message.type == "task_result":
                    self.logger.info("检测到任务结果，停止执行")
                    break

        except Exception as e:
            self.logger.error(f"执行监控过程中出现异常: {e}")
            # 记录异常步骤
            error_step = ExecutionStep(
                step_number=step_counter + 1,
                agent_name="system",
                action_type="error",
                content=f"执行异常: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat(),
                success=False,
                error_info=str(e)
            )
            self.new_execution_steps.append(error_step)

        self.logger.info(f"执行监控完成，新增 {len(self.new_execution_steps)} 个步骤")
        return self.new_execution_steps

    async def _compare_with_original(self, new_step: ExecutionStep):
        """将新步骤与原始执行进行对比"""
        original_step_index = new_step.step_number - 1

        if original_step_index < len(self.original_history):
            original_step = self.original_history[original_step_index]

            comparison = {
                "step_number": new_step.step_number,
                "original_agent": original_step.get("role", "unknown"),
                "new_agent": new_step.agent_name,
                "agent_match": original_step.get("role", "") == new_step.agent_name,
                "content_similarity": self._calculate_content_similarity(
                    original_step.get("content", ""), new_step.content
                ),
                "is_divergence": False
            }

            # 判断是否出现分歧
            if not comparison["agent_match"] or comparison["content_similarity"] < 0.5:
                comparison["is_divergence"] = True
                self.logger.warning(f"步骤 {new_step.step_number} 出现执行分歧")

            self.comparison_results.append(comparison)
        else:
            # 新步骤超出了原始历史长度
            comparison = {
                "step_number": new_step.step_number,
                "original_agent": None,
                "new_agent": new_step.agent_name,
                "agent_match": False,
                "content_similarity": 0.0,
                "is_divergence": True,
                "note": "超出原始执行长度"
            }
            self.comparison_results.append(comparison)

    def _calculate_content_similarity(self, original: str, new: str) -> float:
        """计算内容相似度（简单实现）"""
        if not original and not new:
            return 1.0
        if not original or not new:
            return 0.0

        # 简单的词汇重叠相似度
        original_words = set(original.lower().split())
        new_words = set(new.lower().split())

        if not original_words and not new_words:
            return 1.0

        intersection = original_words.intersection(new_words)
        union = original_words.union(new_words)

        return len(intersection) / len(union) if union else 0.0

    def get_comparison_summary(self) -> Dict[str, Any]:
        """获取对比分析摘要"""
        if not self.comparison_results:
            return {"message": "没有对比数据"}

        total_steps = len(self.comparison_results)
        divergent_steps = sum(1 for comp in self.comparison_results if comp["is_divergence"])
        agent_matches = sum(1 for comp in self.comparison_results if comp["agent_match"])
        avg_similarity = sum(comp["content_similarity"] for comp in self.comparison_results) / total_steps

        return {
            "total_compared_steps": total_steps,
            "divergent_steps": divergent_steps,
            "divergence_rate": divergent_steps / total_steps,
            "agent_match_rate": agent_matches / total_steps,
            "average_content_similarity": avg_similarity,
            "first_divergence_step": next(
                (comp["step_number"] for comp in self.comparison_results if comp["is_divergence"]),
                None
            ),
            "detailed_comparisons": self.comparison_results
        }

# ========================== 场景数据加载模块 ==========================

class ScenarioLoader:
    """场景数据加载器 - 负责加载和验证失败场景数据"""

    @staticmethod
    def load_scenario(scenario_id: str) -> Dict[str, Any]:
        """
        加载指定场景的完整数据

        Args:
            scenario_id: 场景标识符（如 "1", "2" 等）

        Returns:
            包含场景数据的字典
        """
        scenario_path = Config.DATA_DIR / f"{scenario_id}.json"

        if not scenario_path.exists():
            raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")

        try:
            with scenario_path.open(encoding="utf-8") as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"❌ JSON 格式错误: {e}")

        # 验证必要字段
        required_fields = ["history", "question"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"❌ 缺少必要字段: {field}")

        # 打印场景信息
        print(f"\n=== 场景数据加载成功 ===")
        print(f"场景ID: {scenario_id}")
        print(f"原始问题: {data['question'][:100]}...")
        print(f"历史步数: {len(data['history'])}")
        print(f"错误步骤: {data.get('mistake_step', '未标注')}")
        print(f"错误原因: {data.get('mistake_reason', '未标注')}")
        print(f"出错智能体: {data.get('mistake_agent', '未标注')}")

        return data

    @staticmethod
    def validate_breakpoint(history: List[Dict], breakpoint: int) -> None:
        """
        验证断点位置的有效性

        Args:
            history: 消息历史记录
            breakpoint: 断点位置（1-based）
        """
        total_steps = len(history)

        if breakpoint <= 0:
            raise ValueError(f"❌ 断点位置必须大于0，当前值: {breakpoint}")

        if breakpoint > total_steps:
            raise ValueError(f"❌ 断点位置超出范围，最大值: {total_steps}，当前值: {breakpoint}")

        print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

# ========================== 结果保存模块 ==========================

class ResultSaver:
    """结果保存器 - 保存断点复现的完整结果"""

    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)

    def save_reproduction_results(self, scenario_id: str, breakpoint: int, model: str,
                                scenario_data: Dict[str, Any],
                                integrated_logs: Dict[str, Any],
                                restore_stats: Dict[str, Any],
                                execution_steps: List[ExecutionStep],
                                comparison_summary: Dict[str, Any],
                                breakpoint_analysis: BreakpointAnalysis) -> Path:
        """
        保存完整的断点复现结果

        Args:
            scenario_id: 场景ID
            breakpoint: 断点位置
            model: 使用的模型
            scenario_data: 原始场景数据
            integrated_logs: 整合的日志数据
            restore_stats: 状态恢复统计
            execution_steps: 执行步骤列表
            comparison_summary: 对比分析摘要
            breakpoint_analysis: 断点分析结果

        Returns:
            保存文件的路径
        """
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 构建完整的结果数据
        result_data = {
            # 基本信息
            "metadata": {
                "scenario_id": scenario_id,
                "breakpoint_position": breakpoint,
                "model": model,
                "reproduction_timestamp": datetime.now(timezone.utc).isoformat(),
                "reproduction_method": "enhanced_breakpoint_continuation"
            },

            # 原始场景信息
            "original_scenario": {
                "question": scenario_data["question"],
                "ground_truth": scenario_data.get("ground_truth", ""),
                "total_original_steps": len(scenario_data["history"]),
                "mistake_info": {
                    "mistake_step": scenario_data.get("mistake_step"),
                    "mistake_reason": scenario_data.get("mistake_reason"),
                    "mistake_agent": scenario_data.get("mistake_agent")
                }
            },

            # 断点分析结果
            "breakpoint_analysis": asdict(breakpoint_analysis),

            # 状态恢复信息
            "state_restoration": restore_stats,

            # 执行结果
            "execution_results": {
                "continuation_steps": len(execution_steps),
                "execution_timeline": [asdict(step) for step in execution_steps],
                "execution_success": all(step.success for step in execution_steps)
            },

            # 对比分析
            "comparison_analysis": comparison_summary,

            # 完整历史记录
            "complete_history": {
                "original_history": scenario_data["history"][:breakpoint],
                "continuation_history": [asdict(step) for step in execution_steps],
                "full_reproduced_history": scenario_data["history"][:breakpoint] + [
                    {
                        "role": step.agent_name,
                        "content": step.content,
                        "timestamp": step.timestamp,
                        "step_type": "reproduction"
                    } for step in execution_steps
                ]
            },

            # 统计信息
            "statistics": {
                "original_steps": len(scenario_data["history"]),
                "breakpoint_position": breakpoint,
                "continuation_steps": len(execution_steps),
                "total_reproduced_steps": breakpoint + len(execution_steps),
                "state_restore_success_rate": restore_stats.get("injection_stats", {}).get("successful_injections", 0) / max(1, restore_stats.get("injection_stats", {}).get("total_messages", 1)),
                "execution_divergence_rate": comparison_summary.get("divergence_rate", 0.0)
            },

            # 配置信息
            "configuration": {
                "model_config": {
                    "model": model,
                    "temperature": Config.TEMPERATURE,
                    "max_tokens": Config.MAX_TOKENS
                },
                "monitoring_enabled": Config.ENABLE_DETAILED_MONITORING,
                "state_snapshots_enabled": Config.SAVE_STATE_SNAPSHOTS
            }
        }

        # 保存主结果文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"enhanced_reproduction_scenario_{scenario_id}_bp_{breakpoint}_{model}_{timestamp}.json"
        output_path = self.output_dir / output_filename

        with output_path.open("w", encoding="utf-8") as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)

        # 保存简化摘要
        summary_data = {
            "scenario_id": scenario_id,
            "breakpoint": breakpoint,
            "model": model,
            "timestamp": result_data["metadata"]["reproduction_timestamp"],
            "success": result_data["execution_results"]["execution_success"],
            "continuation_steps": result_data["execution_results"]["continuation_steps"],
            "divergence_rate": comparison_summary.get("divergence_rate", 0.0),
            "recommended_breakpoints": breakpoint_analysis.recommended_breakpoints,
            "key_findings": self._extract_key_findings(result_data)
        }

        summary_path = self.output_dir / f"summary_{output_filename}"
        with summary_path.open("w", encoding="utf-8") as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)

        # 打印保存信息
        self.logger.info(f"✓ 断点复现结果已保存:")
        self.logger.info(f"  主文件: {output_path}")
        self.logger.info(f"  摘要文件: {summary_path}")
        self.logger.info(f"  原始步数: {len(scenario_data['history'])}")
        self.logger.info(f"  断点位置: {breakpoint}")
        self.logger.info(f"  继续步数: {len(execution_steps)}")
        self.logger.info(f"  分歧率: {comparison_summary.get('divergence_rate', 0.0):.2%}")

        return output_path

    def _extract_key_findings(self, result_data: Dict[str, Any]) -> List[str]:
        """提取关键发现"""
        findings = []

        # 执行成功性
        if result_data["execution_results"]["execution_success"]:
            findings.append("断点后执行成功完成")
        else:
            findings.append("断点后执行出现错误")

        # 分歧分析
        divergence_rate = result_data["comparison_analysis"].get("divergence_rate", 0.0)
        if divergence_rate > 0.5:
            findings.append(f"高分歧率 ({divergence_rate:.1%})，执行路径显著不同")
        elif divergence_rate > 0.2:
            findings.append(f"中等分歧率 ({divergence_rate:.1%})，部分执行路径不同")
        else:
            findings.append(f"低分歧率 ({divergence_rate:.1%})，执行路径基本一致")

        # 状态恢复
        restore_success = result_data["statistics"]["state_restore_success_rate"]
        if restore_success < 0.8:
            findings.append(f"状态恢复成功率较低 ({restore_success:.1%})")

        # 执行长度对比
        original_length = result_data["statistics"]["original_steps"]
        reproduced_length = result_data["statistics"]["total_reproduced_steps"]
        if reproduced_length > original_length:
            findings.append("复现执行比原始执行更长")
        elif reproduced_length < original_length:
            findings.append("复现执行比原始执行更短")

        return findings

# ========================== 主要执行流程 ==========================

class BreakpointReproductionSystem:
    """断点复现系统主类 - 协调所有模块完成断点复现任务"""

    def __init__(self):
        self.config = Config()
        self.logger = self._setup_logging()

        # 初始化各个模块
        self.scenario_loader = ScenarioLoader()
        self.log_integrator = LogDataIntegrator(Config.LOGS_DIR)
        self.breakpoint_analyzer = BreakpointAnalyzer()
        self.result_saver = ResultSaver(Config.OUTPUT_DIR)

    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logging.basicConfig(
            level=Config.LOG_LEVEL,
            format=Config.LOG_FORMAT,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(Config.OUTPUT_DIR / "reproduction.log")
            ]
        )
        return logging.getLogger(__name__)

    async def run_reproduction(self, scenario_id: str, breakpoint: int, model: str) -> Path:
        """
        执行完整的断点复现流程

        Args:
            scenario_id: 场景标识符
            breakpoint: 断点位置（可以是数字或"auto"）
            model: 使用的模型名称

        Returns:
            保存结果文件的路径
        """
        self.logger.info(f"🚀 === 增强版断点复现任务开始 ===")
        self.logger.info(f"场景ID: {scenario_id}")
        self.logger.info(f"断点位置: {breakpoint}")
        self.logger.info(f"使用模型: {model}")

        try:
            # 步骤1: 验证配置
            self.config.validate()

            # 步骤2: 加载场景数据
            scenario_data = self.scenario_loader.load_scenario(scenario_id)

            # 步骤3: 整合日志数据
            integrated_logs = self.log_integrator.integrate_scenario_logs(scenario_id, model)

            # 步骤4: 断点分析
            breakpoint_analysis = self.breakpoint_analyzer.analyze_breakpoints(
                scenario_data, integrated_logs
            )

            # 步骤5: 处理自动断点选择
            if str(breakpoint).lower() == "auto":
                if breakpoint_analysis.recommended_breakpoints:
                    breakpoint = breakpoint_analysis.recommended_breakpoints[-1]  # 选择最后一个推荐断点
                    self.logger.info(f"自动选择断点位置: {breakpoint}")
                else:
                    # 如果没有推荐断点，使用错误步骤前一步
                    mistake_step = scenario_data.get("mistake_step")
                    if mistake_step and mistake_step > 1:
                        breakpoint = mistake_step - 1
                    else:
                        breakpoint = max(1, len(scenario_data["history"]) // 2)
                    self.logger.info(f"使用默认断点位置: {breakpoint}")

            # 步骤6: 验证断点位置
            self.scenario_loader.validate_breakpoint(scenario_data["history"], breakpoint)

            # 步骤7: 初始化MagenticOne系统
            agent = await self._initialize_magentic_one(model)

            # 步骤8: 增强状态恢复
            state_restorer = EnhancedStateRestorer(agent)
            restore_stats = await state_restorer.restore_complete_state(
                scenario_data, integrated_logs, breakpoint
            )

            # 步骤9: 监控执行
            execution_monitor = ExecutionMonitor(scenario_data["history"], breakpoint)
            execution_steps = await execution_monitor.monitor_execution(
                agent, scenario_data["question"]
            )

            # 步骤10: 获取对比分析
            comparison_summary = execution_monitor.get_comparison_summary()

            # 步骤11: 保存结果
            result_path = self.result_saver.save_reproduction_results(
                scenario_id, breakpoint, model, scenario_data, integrated_logs,
                restore_stats, execution_steps, comparison_summary, breakpoint_analysis
            )

            # 步骤12: 打印总结
            self._print_execution_summary(
                scenario_id, breakpoint, execution_steps, comparison_summary, breakpoint_analysis
            )

            self.logger.info(f"✅ === 断点复现任务完成 ===")
            return result_path

        except Exception as e:
            self.logger.error(f"❌ 断点复现任务失败: {e}")
            raise

    async def _initialize_magentic_one(self, model: str) -> MagenticOne:
        """
        初始化MagenticOne系统

        Args:
            model: 模型名称

        Returns:
            配置好的MagenticOne实例
        """
        self.logger.info(f"=== 初始化MagenticOne系统 ===")

        # 创建模型客户端
        client = OpenAIChatCompletionClient(
            model=model,
            api_key=Config.API_KEY,
            base_url=Config.API_BASE or None,
            temperature=Config.TEMPERATURE,
            model_extras={"max_tokens": Config.MAX_TOKENS}
        )
        self.logger.info(f"✓ 模型客户端创建成功: {model}")

        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()
        self.logger.info(f"✓ 代码执行器创建成功")

        # 创建MagenticOne实例
        agent = MagenticOne(client=client, code_executor=code_executor)
        self.logger.info(f"✓ MagenticOne系统初始化完成")

        return agent

    def _print_execution_summary(self, scenario_id: str, breakpoint: int,
                               execution_steps: List[ExecutionStep],
                               comparison_summary: Dict[str, Any],
                               breakpoint_analysis: BreakpointAnalysis):
        """打印执行摘要"""
        print(f"\n" + "="*60)
        print(f"断点复现执行摘要")
        print(f"="*60)
        print(f"场景ID: {scenario_id}")
        print(f"断点位置: 第 {breakpoint} 步")
        print(f"继续执行步数: {len(execution_steps)}")
        print(f"执行成功: {'是' if all(step.success for step in execution_steps) else '否'}")

        if comparison_summary.get("total_compared_steps", 0) > 0:
            print(f"\n对比分析:")
            print(f"  - 对比步数: {comparison_summary['total_compared_steps']}")
            print(f"  - 分歧率: {comparison_summary.get('divergence_rate', 0.0):.1%}")
            print(f"  - Agent匹配率: {comparison_summary.get('agent_match_rate', 0.0):.1%}")
            print(f"  - 内容相似度: {comparison_summary.get('average_content_similarity', 0.0):.1%}")

            first_divergence = comparison_summary.get('first_divergence_step')
            if first_divergence:
                print(f"  - 首次分歧步骤: 第 {first_divergence} 步")

        print(f"\n断点分析:")
        print(f"  - 推荐断点: {breakpoint_analysis.recommended_breakpoints}")
        print(f"  - 关键决策点: {breakpoint_analysis.critical_decision_points}")
        print(f"  - 状态转换点: {breakpoint_analysis.state_transition_points}")

        print(f"\n" + "="*60)

# ========================== 命令行接口 ==========================

async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(
        description="增强版断点复现系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 从第12步断点开始复现
  python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o

  # 自动选择最佳断点位置
  python breakpoint_reproduction.py --scenario 1 --breakpoint auto --model gpt-4o

  # 使用不同模型
  python breakpoint_reproduction.py --scenario 1 --breakpoint 10 --model gpt-4o-mini

功能特性:
  - 多层次状态恢复（消息历史、Agent状态、环境状态）
  - 智能断点分析和推荐
  - 实时执行监控和对比分析
  - 详细的结果保存和统计
        """
    )

    parser.add_argument(
        "--scenario",
        required=True,
        help="场景ID（如 1, 2, 3 等）"
    )

    parser.add_argument(
        "--breakpoint",
        required=True,
        help="断点位置（步骤编号）或 'auto' 自动选择最佳断点"
    )

    parser.add_argument(
        "--model",
        default=Config.DEFAULT_MODEL,
        help=f"使用的模型（默认: {Config.DEFAULT_MODEL}）"
    )

    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Config.OUTPUT_DIR,
        help=f"输出目录（默认: {Config.OUTPUT_DIR}）"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )

    parser.add_argument(
        "--analyze-only",
        action="store_true",
        help="仅进行断点分析，不执行复现"
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        Config.LOG_LEVEL = logging.DEBUG

    # 设置输出目录
    if args.output_dir:
        Config.OUTPUT_DIR = args.output_dir

    # 处理断点参数
    if args.breakpoint.lower() != "auto":
        try:
            breakpoint = int(args.breakpoint)
        except ValueError:
            print(f"❌ 无效的断点位置: {args.breakpoint}")
            print("断点位置必须是数字或 'auto'")
            return
    else:
        breakpoint = args.breakpoint

    try:
        # 创建断点复现系统
        reproduction_system = BreakpointReproductionSystem()

        if args.analyze_only:
            # 仅进行断点分析
            print("🔍 === 仅进行断点分析模式 ===")
            scenario_data = reproduction_system.scenario_loader.load_scenario(args.scenario)
            integrated_logs = reproduction_system.log_integrator.integrate_scenario_logs(
                args.scenario, args.model
            )
            breakpoint_analysis = reproduction_system.breakpoint_analyzer.analyze_breakpoints(
                scenario_data, integrated_logs
            )

            print("\n断点分析结果:")
            print(breakpoint_analysis.analysis_summary)
            print(f"\n推荐断点位置: {breakpoint_analysis.recommended_breakpoints}")

        else:
            # 执行完整的断点复现
            result_path = await reproduction_system.run_reproduction(
                args.scenario, breakpoint, args.model
            )
            print(f"\n✅ 断点复现完成！结果已保存到: {result_path}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(main())
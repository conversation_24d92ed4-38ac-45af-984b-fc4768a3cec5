#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~
从指定断点位置恢复并继续执行 MagenticOne 系统

核心功能：
1. 解析失败场景的历史记录
2. 恢复到指定断点位置的系统状态
3. 从断点位置继续执行，观察后续行为
4. 详细记录每个步骤的执行过程和结果

主要改进：
- 模块化设计，便于维护和扩展
- 详细的错误处理和状态跟踪
- 结构化的结果保存格式
- 完整的执行统计信息

Usage:
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o
    python breakpoint_reproduction.py --scenario 5 --breakpoint 8 --model gpt-4o-mini
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone

# AutoGen imports
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# ========================== 全局配置类 ==========================
class Config:
    """配置管理类 - 集中管理所有配置参数"""

    # 数据路径配置
    DATA_DIR = Path("Agents_Failure_Attribution/Who&When/Hand-Crafted")
    OUTPUT_DIR = Path("logs/breakpoint_reproduction")

    # API 配置
    API_KEY = os.getenv("OPENAI_API_KEY")
    API_BASE = os.getenv("OPENAI_API_BASE")

    # 执行配置
    DEFAULT_MODEL = "gpt-4o"
    MAX_TOKENS = 512
    TEMPERATURE = 0

    @classmethod
    def validate(cls):
        """验证配置的有效性"""
        if not cls.API_KEY:
            raise ValueError("❌ 请设置 OPENAI_API_KEY 环境变量")

        if not cls.DATA_DIR.exists():
            raise FileNotFoundError(f"❌ 数据目录不存在: {cls.DATA_DIR}")

        # 确保输出目录存在
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓ 配置验证通过，输出目录: {cls.OUTPUT_DIR}")

# ========================== 向后兼容的全局变量 ==========================
# 保持原有代码的兼容性
DATA_DIR = Config.DATA_DIR
OUT_DIR = Config.OUTPUT_DIR
API_KEY = Config.API_KEY
API_BASE = Config.API_BASE

# ========================== 数据加载模块 ==========================
class ScenarioLoader:
    """场景数据加载器 - 负责加载和验证失败场景数据"""

    @staticmethod
    def load_scenario(scenario_id: str) -> Dict[str, Any]:
        """
        加载指定场景的完整数据

        Args:
            scenario_id: 场景标识符（如 "1", "2" 等）

        Returns:
            包含以下字段的字典：
            - history: 消息历史记录列表
            - question: 原始用户问题
            - mistake_step: 错误发生的步骤编号
            - mistake_reason: 错误原因描述
            - mistake_agent: 出错的智能体名称

        Raises:
            FileNotFoundError: 场景文件不存在
            json.JSONDecodeError: JSON 格式错误
        """
        scenario_path = Config.DATA_DIR / f"{scenario_id}.json"

        if not scenario_path.exists():
            raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")

        try:
            with scenario_path.open(encoding="utf-8") as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"❌ JSON 格式错误: {e}")

        # 验证必要字段
        required_fields = ["history", "question"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"❌ 缺少必要字段: {field}")

        # 打印场景信息
        print(f"\n=== 场景数据加载成功 ===")
        print(f"场景ID: {scenario_id}")
        print(f"原始问题: {data['question'][:100]}...")
        print(f"历史步数: {len(data['history'])}")
        print(f"错误步骤: {data.get('mistake_step', '未标注')}")
        print(f"错误原因: {data.get('mistake_reason', '未标注')}")
        print(f"出错智能体: {data.get('mistake_agent', '未标注')}")

        return data

    @staticmethod
    def validate_breakpoint(history: List[Dict], breakpoint: int) -> None:
        """
        验证断点位置的有效性

        Args:
            history: 消息历史记录
            breakpoint: 断点位置（1-based）

        Raises:
            ValueError: 断点位置无效
        """
        total_steps = len(history)

        if breakpoint <= 0:
            raise ValueError(f"❌ 断点位置必须大于0，当前值: {breakpoint}")

        if breakpoint > total_steps:
            raise ValueError(f"❌ 断点位置超出范围，最大值: {total_steps}，当前值: {breakpoint}")

        print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

# ========================== 向后兼容函数 ==========================
def load_scenario_data(scenario_id: str) -> Dict[str, Any]:
    """向后兼容的场景加载函数"""
    return ScenarioLoader.load_scenario(scenario_id)

# ========================== 状态恢复模块 ==========================
async def inject_message_to_runtime(agent: MagenticOne, message_data: Dict[str, Any]) -> bool:
    """
    将单条历史消息注入到 MagenticOne 运行时
    
    Args:
        agent: MagenticOne 实例
        message_data: 消息数据，包含 role 和 content
        
    Returns:
        注入是否成功
    """
    try:
        role = message_data.get("role", "assistant")
        content = message_data.get("content", "")
        
        # 创建 TextMessage 对象
        message = TextMessage(source=role, content=content)
        
        # 尝试多种注入方式
        if hasattr(agent, "_runtime") and hasattr(agent._runtime, "publish"):
            await agent._runtime.publish(message)
        elif hasattr(agent, "runtime") and hasattr(agent.runtime, "publish"):
            await agent.runtime.publish(message)
        elif hasattr(agent, "send"):
            await agent.send(message)
        else:
            print(f"[警告] 无法找到消息注入接口，跳过消息: [{role}]")
            return False
            
        return True
        
    except Exception as e:
        print(f"[错误] 消息注入失败: {e}")
        return False

async def restore_system_state(agent: MagenticOne, history: List[Dict[str, Any]], breakpoint: int) -> int:
    """
    恢复系统状态到指定断点位置
    
    Args:
        agent: MagenticOne 实例
        history: 完整的消息历史记录
        breakpoint: 断点位置（步骤编号）
        
    Returns:
        实际注入的消息数量
    """
    print(f"\n=== 开始状态恢复 ===")
    print(f"目标断点位置: 第 {breakpoint} 步")
    print(f"需要恢复的消息数量: {breakpoint}")
    
    injected_count = 0
    failed_count = 0
    
    # 注入断点之前的所有消息
    for i, message_data in enumerate(history[:breakpoint]):
        step_num = i + 1
        role = message_data.get("role", "unknown")
        content_preview = str(message_data.get("content", ""))[:60].replace('\n', ' ')
        
        print(f"[步骤 {step_num:2d}] 注入消息 [{role}]: {content_preview}...")
        
        success = await inject_message_to_runtime(agent, message_data)
        if success:
            injected_count += 1
        else:
            failed_count += 1
    
    print(f"\n状态恢复完成:")
    print(f"  ✓ 成功注入: {injected_count} 条消息")
    print(f"  ✗ 注入失败: {failed_count} 条消息")
    print(f"  → 系统已恢复到第 {breakpoint} 步的状态")
    
    return injected_count

# ========================== 断点执行模块 ==========================
async def continue_from_breakpoint(agent: MagenticOne, original_question: str, breakpoint_step: int) -> List[Dict[str, Any]]:
    """
    从断点位置继续执行系统推理
    
    Args:
        agent: 已恢复状态的 MagenticOne 实例
        original_question: 原始问题
        breakpoint_step: 断点步骤编号
        
    Returns:
        从断点开始的新消息历史记录
    """
    print(f"\n=== 从断点继续执行 ===")
    print(f"断点位置: 第 {breakpoint_step} 步")
    print(f"原始问题: {original_question}")
    print(f"开始继续推理...\n")
    
    continuation_history = []
    step_counter = breakpoint_step
    
    try:
        # 使用 run_stream 从当前状态继续执行
        async for message in agent.run_stream(task=original_question):
            step_counter += 1
            
            # 提取消息信息
            role = getattr(message, "role", None) or getattr(message, "source", "assistant")
            content = getattr(message, "content", "")
            timestamp = getattr(message, "created_at", datetime.now(timezone.utc))
            
            # 控制台输出（截断显示）
            content_preview = str(content)[:100].replace('\n', ' ')
            print(f"[步骤 {step_counter:2d}] [{role}] {content_preview}...")
            
            # 记录到历史
            message_record = {
                "step": step_counter,
                "role": role,
                "content": str(content),
                "timestamp": timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp)
            }
            continuation_history.append(message_record)
            
            # 检查是否为任务结果
            if hasattr(message, "type") and message.type == "task_result":
                print(f"\n✓ 检测到任务结果，停止执行")
                break
                
    except Exception as e:
        print(f"\n[错误] 执行过程中出现异常: {e}")
        print(f"已执行步数: {len(continuation_history)}")
    
    print(f"\n断点继续执行完成，新增 {len(continuation_history)} 步")
    return continuation_history

# ========================== 结果保存模块 ==========================
def save_breakpoint_reproduction(scenario_id: str, breakpoint: int, model: str, 
                                original_data: Dict[str, Any], continuation_history: List[Dict[str, Any]]) -> None:
    """
    保存断点复现的完整结果
    
    Args:
        scenario_id: 场景ID
        breakpoint: 断点位置
        model: 使用的模型
        original_data: 原始场景数据
        continuation_history: 从断点继续的历史记录
    """
    OUT_DIR.mkdir(parents=True, exist_ok=True)
    
    # 构建完整的执行历史（原始历史 + 断点继续历史）
    full_history = original_data["history"][:breakpoint] + continuation_history
    
    # 构建输出数据
    output_data = {
        "scenario_id": scenario_id,
        "reproduction_method": "breakpoint_continuation",
        "breakpoint_step": breakpoint,
        "model": model,
        "original_question": original_data["question"],
        "ground_truth": original_data.get("ground_truth", ""),
        
        # 原始失败信息
        "original_failure": {
            "mistake_step": original_data.get("mistake_step"),
            "mistake_reason": original_data.get("mistake_reason"),
            "mistake_agent": original_data.get("mistake_agent")
        },
        
        # 执行统计
        "execution_stats": {
            "original_steps": len(original_data["history"]),
            "breakpoint_position": breakpoint,
            "continuation_steps": len(continuation_history),
            "total_reproduced_steps": len(full_history)
        },
        
        # 完整历史记录
        "full_history": full_history,
        "continuation_only": continuation_history,
        
        # 元数据
        "reproduced_at": datetime.now(timezone.utc).isoformat(),
        "reproduction_config": {
            "inject_up_to_step": breakpoint,
            "continue_from_step": breakpoint + 1
        }
    }
    
    # 保存文件
    output_path = OUT_DIR / f"scenario_{scenario_id}_breakpoint_{breakpoint}_{model}.json"
    with output_path.open("w", encoding="utf-8") as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✓ 断点复现结果已保存到: {output_path}")
    print(f"  - 原始步数: {len(original_data['history'])}")
    print(f"  - 断点位置: {breakpoint}")
    print(f"  - 继续步数: {len(continuation_history)}")
    print(f"  - 总计步数: {len(full_history)}")

# ========================== 主执行流程 ==========================
async def run_breakpoint_reproduction(scenario_id: str, breakpoint: int, model: str) -> None:
    """
    执行完整的断点复现流程
    
    Args:
        scenario_id: 场景ID
        breakpoint: 断点位置
        model: 使用的模型
    """
    print(f"=== 断点复现任务开始 ===")
    print(f"场景ID: {scenario_id}")
    print(f"断点位置: 第 {breakpoint} 步")
    print(f"使用模型: {model}")
    
    # 1. 加载场景数据
    scenario_data = load_scenario_data(scenario_id)
    
    # 2. 验证断点位置
    total_steps = len(scenario_data["history"])
    if breakpoint <= 0 or breakpoint > total_steps:
        raise ValueError(f"断点位置无效: {breakpoint}，有效范围: 1-{total_steps}")
    
    # 3. 初始化 MagenticOne 系统
    print(f"\n=== 初始化系统 ===")
    client = OpenAIChatCompletionClient(
        model=model,
        api_key=API_KEY,
        base_url=API_BASE or None,
        temperature=0,
        model_extras={"max_tokens": 512}
    )
    
    code_executor = LocalCommandLineCodeExecutor()
    agent = MagenticOne(client=client, code_executor=code_executor)
    print(f"✓ MagenticOne 系统初始化完成")
    
    # 4. 恢复系统状态到断点
    injected_count = await restore_system_state(agent, scenario_data["history"], breakpoint)
    
    # 5. 从断点继续执行
    continuation_history = await continue_from_breakpoint(
        agent, scenario_data["question"], breakpoint
    )
    
    # 6. 保存复现结果
    save_breakpoint_reproduction(
        scenario_id, breakpoint, model, scenario_data, continuation_history
    )
    
    print(f"\n=== 断点复现任务完成 ===")

# ========================== 命令行接口 ==========================
async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(description="从指定断点位置复现 MagenticOne 执行")
    parser.add_argument("--scenario", required=True, help="场景ID（如 1）")
    parser.add_argument("--breakpoint", type=int, required=True, help="断点位置（步骤编号）")
    parser.add_argument("--model", default="gpt-4o", help="使用的模型（默认: gpt-4o）")
    
    args = parser.parse_args()
    
    try:
        await run_breakpoint_reproduction(args.scenario, args.breakpoint, args.model)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~
简化版断点恢复系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统

核心功能：
1. 从错误步骤开始往前恢复执行
2. 支持多种模型（ollama、openai、cloudgpt）
3. 简单的消息注入和继续执行
4. 保存复现结果

Usage:
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12                    # 默认使用 ollama llama3.1
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --endpoint ollama --model llama3.1
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --endpoint openai --model gpt-4o
    python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --endpoint cloudgpt --model gpt-4o-2024-05-13
"""

import os
import json
import argparse
import asyncio
from pathlib import Path
from typing import Any, Dict, List
from datetime import datetime, timezone

# AutoGen imports
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# ========================== 配置和路径 ==========================

# API 配置
API_KEY = os.getenv("OPENAI_API_KEY")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
OLLAMA_HOST = "http://localhost:11434"

# 路径配置
DATA_DIR = Path("Agents_Failure_Attribution/Who&When/Hand-Crafted")
OUTPUT_DIR = Path("logs/breakpoint_reproduction")

# ========================== 模型客户端创建 ==========================

def get_client(endpoint_type: str = "ollama", model: str = None):
    """获取适当的模型客户端"""
    if endpoint_type == "ollama":
        return OllamaChatCompletionClient(
            model=model or "llama3.1",
            host=OLLAMA_HOST,
        )
    elif endpoint_type == "openai":
        if not API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable must be set for OpenAI usage")
        return OpenAIChatCompletionClient(
            model=model or "gpt-4o",
            api_key=API_KEY,
            base_url=API_BASE,
        )
    elif endpoint_type == "cloudgpt":
        if not model:
            raise ValueError("Model name must be specified for cloudgpt")
        return AzureOpenAIChatCompletionClient(
            model=model,
        )
    else:
        raise ValueError(f"Unknown endpoint type: {endpoint_type}")

# ========================== 消息注入工具 ==========================

async def inject_message(agent: MagenticOne, message_data: Dict[str, Any]) -> bool:
    """将单条历史消息注入到 MagenticOne 运行时"""
    try:
        role = message_data.get("role", "assistant")
        content = message_data.get("content", "")

        # 创建 TextMessage 对象
        message = TextMessage(source=role, content=content)

        # 尝试多种注入方式
        if hasattr(agent, "_runtime") and hasattr(agent._runtime, "publish"):
            await agent._runtime.publish(message)
        elif hasattr(agent, "runtime") and hasattr(agent.runtime, "publish"):
            await agent.runtime.publish(message)
        elif hasattr(agent, "send"):
            await agent.send(message)
        else:
            print(f"[警告] 无法找到消息注入接口，跳过消息: [{role}]")
            return False

        return True

    except Exception as e:
        print(f"[错误] 消息注入失败: {e}")
        return False

# ========================== 场景数据加载 ==========================

def load_scenario(scenario_id: str) -> Dict[str, Any]:
    """加载场景数据"""
    scenario_path = DATA_DIR / f"{scenario_id}.json"

    if not scenario_path.exists():
        raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")

    with scenario_path.open(encoding="utf-8") as f:
        data = json.load(f)

    print(f"\n=== 场景数据加载成功 ===")
    print(f"场景ID: {scenario_id}")
    print(f"原始问题: {data['question'][:100]}...")
    print(f"历史步数: {len(data['history'])}")
    print(f"错误步骤: {data.get('mistake_step', '未标注')}")
    print(f"错误原因: {data.get('mistake_reason', '未标注')}")
    print(f"出错智能体: {data.get('mistake_agent', '未标注')}")

    return data

# ========================== 断点复现主要功能 ==========================

async def restore_from_breakpoint(scenario_id: str, breakpoint: int = None, endpoint_type: str = "ollama", model: str = None):
    """从指定断点位置恢复并继续执行"""

    print(f"\n🚀 === 断点复现开始 ===")
    print(f"场景ID: {scenario_id}")
    print(f"断点位置: 第 {breakpoint} 步")
    print(f"端点类型: {endpoint_type}")
    print(f"模型: {model or ('llama3.1' if endpoint_type == 'ollama' else 'default')}")

    # 1. 加载场景数据
    scenario_data = load_scenario(scenario_id)

    # 2. 自动获取或验证断点位置
    if breakpoint is None:
        # 从场景数据中自动读取错误步骤
        mistake_step = scenario_data.get("mistake_step")
        if mistake_step:
            # mistake_step 可能是字符串，需要转换为整数
            breakpoint = int(mistake_step)
            print(f"✓ 自动从场景数据读取断点位置: 第 {breakpoint} 步")
            print(f"  错误原因: {scenario_data.get('mistake_reason', '未知')}")
            print(f"  出错智能体: {scenario_data.get('mistake_agent', '未知')}")
        else:
            raise ValueError(f"❌ 场景数据中未找到 mistake_step 字段，请手动指定断点位置")

    # 验证断点位置
    total_steps = len(scenario_data["history"])
    if breakpoint <= 0 or breakpoint > total_steps:
        raise ValueError(f"❌ 断点位置无效: {breakpoint}，有效范围: 1-{total_steps}")

    print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

    # 3. 初始化 MagenticOne 系统
    print(f"\n=== 初始化 MagenticOne 系统 ===")
    client = get_client(endpoint_type, model)
    code_executor = LocalCommandLineCodeExecutor()
    agent = MagenticOne(client=client, code_executor=code_executor)
    print(f"✓ MagenticOne 系统初始化完成")

    # 4. 注入历史消息到断点位置
    print(f"\n=== 注入历史消息到第 {breakpoint} 步 ===")
    successful_injections = 0
    failed_injections = 0

    for i, message_data in enumerate(scenario_data["history"][:breakpoint]):
        step_num = i + 1
        role = message_data.get("role", "unknown")
        content_preview = str(message_data.get("content", ""))[:60].replace('\n', ' ')

        print(f"[步骤 {step_num:2d}] 注入消息 [{role}]: {content_preview}...")

        success = await inject_message(agent, message_data)
        if success:
            successful_injections += 1
        else:
            failed_injections += 1

    print(f"\n状态恢复完成:")
    print(f"  ✓ 成功注入: {successful_injections} 条消息")
    print(f"  ✗ 注入失败: {failed_injections} 条消息")
    print(f"  → 系统已恢复到第 {breakpoint} 步的状态")

    # 5. 从断点继续执行
    print(f"\n=== 从断点继续执行 ===")
    continuation_history = []
    step_counter = breakpoint

    try:
        # 使用 run_stream 从当前状态继续执行
        async for message in agent.run_stream(task=scenario_data["question"]):
            step_counter += 1

            # 提取消息信息
            role = getattr(message, "role", None) or getattr(message, "source", "assistant")
            content = getattr(message, "content", "")
            timestamp = getattr(message, "created_at", datetime.now(timezone.utc))

            # 控制台输出（截断显示）
            content_preview = str(content)[:100].replace('\n', ' ')
            print(f"[步骤 {step_counter:2d}] [{role}] {content_preview}...")

            # 记录到历史
            message_record = {
                "step": step_counter,
                "role": role,
                "content": str(content),
                "timestamp": timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp)
            }
            continuation_history.append(message_record)

            # 检查是否为任务结果
            if hasattr(message, "type") and message.type == "task_result":
                print(f"\n✓ 检测到任务结果，停止执行")
                break

    except Exception as e:
        print(f"\n[错误] 执行过程中出现异常: {e}")
        print(f"已执行步数: {len(continuation_history)}")

    print(f"\n断点继续执行完成，新增 {len(continuation_history)} 步")

    return scenario_data, continuation_history, successful_injections, failed_injections

# ========================== 结果保存 ==========================

def save_reproduction_results(scenario_id: str, breakpoint: int, endpoint_type: str, model: str,
                            scenario_data: Dict[str, Any], continuation_history: List[Dict[str, Any]],
                            successful_injections: int, failed_injections: int) -> Path:
    """保存断点复现结果"""

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # 构建完整的执行历史（原始历史 + 断点继续历史）
    full_history = scenario_data["history"][:breakpoint] + continuation_history

    # 构建输出数据
    output_data = {
        "scenario_id": scenario_id,
        "reproduction_method": "simple_breakpoint_continuation",
        "breakpoint_step": breakpoint,
        "endpoint_type": endpoint_type,
        "model": model or ("llama3.1" if endpoint_type == "ollama" else "default"),
        "original_question": scenario_data["question"],
        "ground_truth": scenario_data.get("ground_truth", ""),

        # 原始失败信息
        "original_failure": {
            "mistake_step": scenario_data.get("mistake_step"),
            "mistake_reason": scenario_data.get("mistake_reason"),
            "mistake_agent": scenario_data.get("mistake_agent")
        },

        # 执行统计
        "execution_stats": {
            "original_steps": len(scenario_data["history"]),
            "breakpoint_position": breakpoint,
            "continuation_steps": len(continuation_history),
            "total_reproduced_steps": len(full_history),
            "successful_injections": successful_injections,
            "failed_injections": failed_injections,
            "injection_success_rate": successful_injections / max(1, successful_injections + failed_injections)
        },

        # 完整历史记录
        "full_history": full_history,
        "continuation_only": continuation_history,

        # 元数据
        "reproduced_at": datetime.now(timezone.utc).isoformat(),
        "reproduction_config": {
            "inject_up_to_step": breakpoint,
            "continue_from_step": breakpoint + 1
        }
    }

    # 保存文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"simple_reproduction_scenario_{scenario_id}_bp_{breakpoint}_{endpoint_type}_{model or 'default'}_{timestamp}.json"
    output_path = OUTPUT_DIR / output_filename

    with output_path.open("w", encoding="utf-8") as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"\n✓ 断点复现结果已保存到: {output_path}")
    print(f"  - 原始步数: {len(scenario_data['history'])}")
    print(f"  - 断点位置: {breakpoint}")
    print(f"  - 继续步数: {len(continuation_history)}")
    print(f"  - 总计步数: {len(full_history)}")
    print(f"  - 注入成功率: {successful_injections}/{successful_injections + failed_injections}")

    return output_path

# ========================== 命令行接口 ==========================

async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(
        description="简化版断点复现系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 自动读取错误步骤，默认使用 ollama llama3.1
  python breakpoint_reproduction.py --scenario 1

  # 自动读取错误步骤，使用 OpenAI
  python breakpoint_reproduction.py --scenario 1 --endpoint openai --model gpt-4o

  # 手动指定断点位置
  python breakpoint_reproduction.py --scenario 1 --breakpoint 12

  # 使用 CloudGPT
  python breakpoint_reproduction.py --scenario 1 --endpoint cloudgpt --model gpt-4o-2024-05-13
        """
    )

    parser.add_argument(
        "--scenario",
        required=True,
        help="场景ID（如 1, 2, 3 等）"
    )

    parser.add_argument(
        "--breakpoint",
        type=int,
        help="断点位置（步骤编号），如果不指定则自动从场景数据中读取 mistake_step"
    )

    parser.add_argument(
        "--endpoint",
        choices=["ollama", "openai", "cloudgpt"],
        default="ollama",
        help="端点类型（默认: ollama）"
    )

    parser.add_argument(
        "--model",
        help="模型名称（ollama默认: llama3.1, openai默认: gpt-4o）"
    )

    args = parser.parse_args()

    try:
        print(f"🚀 开始断点复现任务...")

        # 执行断点复现
        scenario_data, continuation_history, successful_injections, failed_injections = await restore_from_breakpoint(
            args.scenario, args.breakpoint, args.endpoint, args.model
        )

        # 获取实际使用的断点位置（可能是自动读取的）
        actual_breakpoint = args.breakpoint
        if actual_breakpoint is None:
            mistake_step = scenario_data.get("mistake_step")
            if mistake_step:
                actual_breakpoint = int(mistake_step)

        # 保存结果
        result_path = save_reproduction_results(
            args.scenario, actual_breakpoint, args.endpoint, args.model,
            scenario_data, continuation_history, successful_injections, failed_injections
        )

        # 打印总结
        print(f"\n" + "="*60)
        print(f"断点复现执行摘要")
        print(f"="*60)
        print(f"场景ID: {args.scenario}")
        print(f"断点位置: 第 {actual_breakpoint} 步" + (" (自动读取)" if args.breakpoint is None else " (手动指定)"))
        print(f"端点类型: {args.endpoint}")
        print(f"模型: {args.model or ('llama3.1' if args.endpoint == 'ollama' else 'default')}")
        print(f"继续执行步数: {len(continuation_history)}")
        print(f"注入成功率: {successful_injections}/{successful_injections + failed_injections}")
        print(f"结果文件: {result_path}")
        print(f"="*60)

        print(f"\n✅ 断点复现任务完成！")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

# ========================== 场景数据加载模块 ==========================

class ScenarioLoader:
    """场景数据加载器 - 负责加载和验证失败场景数据"""

    @staticmethod
    def load_scenario(scenario_id: str) -> Dict[str, Any]:
        """
        加载指定场景的完整数据

        Args:
            scenario_id: 场景标识符（如 "1", "2" 等）

        Returns:
            包含场景数据的字典
        """
        scenario_path = Config.DATA_DIR / f"{scenario_id}.json"

        if not scenario_path.exists():
            raise FileNotFoundError(f"❌ 场景文件不存在: {scenario_path}")

        try:
            with scenario_path.open(encoding="utf-8") as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"❌ JSON 格式错误: {e}")

        # 验证必要字段
        required_fields = ["history", "question"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"❌ 缺少必要字段: {field}")

        # 打印场景信息
        print(f"\n=== 场景数据加载成功 ===")
        print(f"场景ID: {scenario_id}")
        print(f"原始问题: {data['question'][:100]}...")
        print(f"历史步数: {len(data['history'])}")
        print(f"错误步骤: {data.get('mistake_step', '未标注')}")
        print(f"错误原因: {data.get('mistake_reason', '未标注')}")
        print(f"出错智能体: {data.get('mistake_agent', '未标注')}")

        return data

    @staticmethod
    def validate_breakpoint(history: List[Dict], breakpoint: int) -> None:
        """
        验证断点位置的有效性

        Args:
            history: 消息历史记录
            breakpoint: 断点位置（1-based）
        """
        total_steps = len(history)

        if breakpoint <= 0:
            raise ValueError(f"❌ 断点位置必须大于0，当前值: {breakpoint}")

        if breakpoint > total_steps:
            raise ValueError(f"❌ 断点位置超出范围，最大值: {total_steps}，当前值: {breakpoint}")

        print(f"✓ 断点位置验证通过: {breakpoint}/{total_steps}")

# ========================== 结果保存模块 ==========================

class ResultSaver:
    """结果保存器 - 保存断点复现的完整结果"""

    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)

    def save_reproduction_results(self, scenario_id: str, breakpoint: int, model: str,
                                scenario_data: Dict[str, Any],
                                integrated_logs: Dict[str, Any],
                                restore_stats: Dict[str, Any],
                                execution_steps: List[ExecutionStep],
                                comparison_summary: Dict[str, Any],
                                breakpoint_analysis: BreakpointAnalysis) -> Path:
        """
        保存完整的断点复现结果

        Args:
            scenario_id: 场景ID
            breakpoint: 断点位置
            model: 使用的模型
            scenario_data: 原始场景数据
            integrated_logs: 整合的日志数据
            restore_stats: 状态恢复统计
            execution_steps: 执行步骤列表
            comparison_summary: 对比分析摘要
            breakpoint_analysis: 断点分析结果

        Returns:
            保存文件的路径
        """
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 构建完整的结果数据
        result_data = {
            # 基本信息
            "metadata": {
                "scenario_id": scenario_id,
                "breakpoint_position": breakpoint,
                "model": model,
                "reproduction_timestamp": datetime.now(timezone.utc).isoformat(),
                "reproduction_method": "enhanced_breakpoint_continuation"
            },

            # 原始场景信息
            "original_scenario": {
                "question": scenario_data["question"],
                "ground_truth": scenario_data.get("ground_truth", ""),
                "total_original_steps": len(scenario_data["history"]),
                "mistake_info": {
                    "mistake_step": scenario_data.get("mistake_step"),
                    "mistake_reason": scenario_data.get("mistake_reason"),
                    "mistake_agent": scenario_data.get("mistake_agent")
                }
            },

            # 断点分析结果
            "breakpoint_analysis": asdict(breakpoint_analysis),

            # 状态恢复信息
            "state_restoration": restore_stats,

            # 执行结果
            "execution_results": {
                "continuation_steps": len(execution_steps),
                "execution_timeline": [asdict(step) for step in execution_steps],
                "execution_success": all(step.success for step in execution_steps)
            },

            # 对比分析
            "comparison_analysis": comparison_summary,

            # 完整历史记录
            "complete_history": {
                "original_history": scenario_data["history"][:breakpoint],
                "continuation_history": [asdict(step) for step in execution_steps],
                "full_reproduced_history": scenario_data["history"][:breakpoint] + [
                    {
                        "role": step.agent_name,
                        "content": step.content,
                        "timestamp": step.timestamp,
                        "step_type": "reproduction"
                    } for step in execution_steps
                ]
            },

            # 统计信息
            "statistics": {
                "original_steps": len(scenario_data["history"]),
                "breakpoint_position": breakpoint,
                "continuation_steps": len(execution_steps),
                "total_reproduced_steps": breakpoint + len(execution_steps),
                "state_restore_success_rate": restore_stats.get("injection_stats", {}).get("successful_injections", 0) / max(1, restore_stats.get("injection_stats", {}).get("total_messages", 1)),
                "execution_divergence_rate": comparison_summary.get("divergence_rate", 0.0)
            },

            # 配置信息
            "configuration": {
                "model_config": {
                    "model": model,
                    "temperature": Config.TEMPERATURE,
                    "max_tokens": Config.MAX_TOKENS
                },
                "monitoring_enabled": Config.ENABLE_DETAILED_MONITORING,
                "state_snapshots_enabled": Config.SAVE_STATE_SNAPSHOTS
            }
        }

        # 保存主结果文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"enhanced_reproduction_scenario_{scenario_id}_bp_{breakpoint}_{model}_{timestamp}.json"
        output_path = self.output_dir / output_filename

        with output_path.open("w", encoding="utf-8") as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)

        # 保存简化摘要
        summary_data = {
            "scenario_id": scenario_id,
            "breakpoint": breakpoint,
            "model": model,
            "timestamp": result_data["metadata"]["reproduction_timestamp"],
            "success": result_data["execution_results"]["execution_success"],
            "continuation_steps": result_data["execution_results"]["continuation_steps"],
            "divergence_rate": comparison_summary.get("divergence_rate", 0.0),
            "recommended_breakpoints": breakpoint_analysis.recommended_breakpoints,
            "key_findings": self._extract_key_findings(result_data)
        }

        summary_path = self.output_dir / f"summary_{output_filename}"
        with summary_path.open("w", encoding="utf-8") as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)

        # 打印保存信息
        self.logger.info(f"✓ 断点复现结果已保存:")
        self.logger.info(f"  主文件: {output_path}")
        self.logger.info(f"  摘要文件: {summary_path}")
        self.logger.info(f"  原始步数: {len(scenario_data['history'])}")
        self.logger.info(f"  断点位置: {breakpoint}")
        self.logger.info(f"  继续步数: {len(execution_steps)}")
        self.logger.info(f"  分歧率: {comparison_summary.get('divergence_rate', 0.0):.2%}")

        return output_path

    def _extract_key_findings(self, result_data: Dict[str, Any]) -> List[str]:
        """提取关键发现"""
        findings = []

        # 执行成功性
        if result_data["execution_results"]["execution_success"]:
            findings.append("断点后执行成功完成")
        else:
            findings.append("断点后执行出现错误")

        # 分歧分析
        divergence_rate = result_data["comparison_analysis"].get("divergence_rate", 0.0)
        if divergence_rate > 0.5:
            findings.append(f"高分歧率 ({divergence_rate:.1%})，执行路径显著不同")
        elif divergence_rate > 0.2:
            findings.append(f"中等分歧率 ({divergence_rate:.1%})，部分执行路径不同")
        else:
            findings.append(f"低分歧率 ({divergence_rate:.1%})，执行路径基本一致")

        # 状态恢复
        restore_success = result_data["statistics"]["state_restore_success_rate"]
        if restore_success < 0.8:
            findings.append(f"状态恢复成功率较低 ({restore_success:.1%})")

        # 执行长度对比
        original_length = result_data["statistics"]["original_steps"]
        reproduced_length = result_data["statistics"]["total_reproduced_steps"]
        if reproduced_length > original_length:
            findings.append("复现执行比原始执行更长")
        elif reproduced_length < original_length:
            findings.append("复现执行比原始执行更短")

        return findings

# ========================== 主要执行流程 ==========================

class BreakpointReproductionSystem:
    """断点复现系统主类 - 协调所有模块完成断点复现任务"""

    def __init__(self):
        self.config = Config()
        self.logger = self._setup_logging()

        # 初始化各个模块
        self.scenario_loader = ScenarioLoader()
        self.log_integrator = LogDataIntegrator(Config.LOGS_DIR)
        self.breakpoint_analyzer = BreakpointAnalyzer()
        self.result_saver = ResultSaver(Config.OUTPUT_DIR)

    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logging.basicConfig(
            level=Config.LOG_LEVEL,
            format=Config.LOG_FORMAT,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(Config.OUTPUT_DIR / "reproduction.log")
            ]
        )
        return logging.getLogger(__name__)

    async def run_reproduction(self, scenario_id: str, breakpoint: int, model: str) -> Path:
        """
        执行完整的断点复现流程

        Args:
            scenario_id: 场景标识符
            breakpoint: 断点位置（可以是数字或"auto"）
            model: 使用的模型名称

        Returns:
            保存结果文件的路径
        """
        self.logger.info(f"🚀 === 增强版断点复现任务开始 ===")
        self.logger.info(f"场景ID: {scenario_id}")
        self.logger.info(f"断点位置: {breakpoint}")
        self.logger.info(f"使用模型: {model}")

        try:
            # 步骤1: 验证配置
            self.config.validate()

            # 步骤2: 加载场景数据
            scenario_data = self.scenario_loader.load_scenario(scenario_id)

            # 步骤3: 整合日志数据
            integrated_logs = self.log_integrator.integrate_scenario_logs(scenario_id, model)

            # 步骤4: 断点分析
            breakpoint_analysis = self.breakpoint_analyzer.analyze_breakpoints(
                scenario_data, integrated_logs
            )

            # 步骤5: 处理自动断点选择
            if str(breakpoint).lower() == "auto":
                if breakpoint_analysis.recommended_breakpoints:
                    breakpoint = breakpoint_analysis.recommended_breakpoints[-1]  # 选择最后一个推荐断点
                    self.logger.info(f"自动选择断点位置: {breakpoint}")
                else:
                    # 如果没有推荐断点，使用错误步骤前一步
                    mistake_step = scenario_data.get("mistake_step")
                    if mistake_step and mistake_step > 1:
                        breakpoint = mistake_step - 1
                    else:
                        breakpoint = max(1, len(scenario_data["history"]) // 2)
                    self.logger.info(f"使用默认断点位置: {breakpoint}")

            # 步骤6: 验证断点位置
            self.scenario_loader.validate_breakpoint(scenario_data["history"], breakpoint)

            # 步骤7: 初始化MagenticOne系统
            agent = await self._initialize_magentic_one(model)

            # 步骤8: 增强状态恢复
            state_restorer = EnhancedStateRestorer(agent)
            restore_stats = await state_restorer.restore_complete_state(
                scenario_data, integrated_logs, breakpoint
            )

            # 步骤9: 监控执行
            execution_monitor = ExecutionMonitor(scenario_data["history"], breakpoint)
            execution_steps = await execution_monitor.monitor_execution(
                agent, scenario_data["question"]
            )

            # 步骤10: 获取对比分析
            comparison_summary = execution_monitor.get_comparison_summary()

            # 步骤11: 保存结果
            result_path = self.result_saver.save_reproduction_results(
                scenario_id, breakpoint, model, scenario_data, integrated_logs,
                restore_stats, execution_steps, comparison_summary, breakpoint_analysis
            )

            # 步骤12: 打印总结
            self._print_execution_summary(
                scenario_id, breakpoint, execution_steps, comparison_summary, breakpoint_analysis
            )

            self.logger.info(f"✅ === 断点复现任务完成 ===")
            return result_path

        except Exception as e:
            self.logger.error(f"❌ 断点复现任务失败: {e}")
            raise

    async def _initialize_magentic_one(self, model: str) -> MagenticOne:
        """
        初始化MagenticOne系统

        Args:
            model: 模型名称

        Returns:
            配置好的MagenticOne实例
        """
        self.logger.info(f"=== 初始化MagenticOne系统 ===")

        # 创建模型客户端
        client = OpenAIChatCompletionClient(
            model=model,
            api_key=Config.API_KEY,
            base_url=Config.API_BASE or None,
            temperature=Config.TEMPERATURE,
            model_extras={"max_tokens": Config.MAX_TOKENS}
        )
        self.logger.info(f"✓ 模型客户端创建成功: {model}")

        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()
        self.logger.info(f"✓ 代码执行器创建成功")

        # 创建MagenticOne实例
        agent = MagenticOne(client=client, code_executor=code_executor)
        self.logger.info(f"✓ MagenticOne系统初始化完成")

        return agent

    def _print_execution_summary(self, scenario_id: str, breakpoint: int,
                               execution_steps: List[ExecutionStep],
                               comparison_summary: Dict[str, Any],
                               breakpoint_analysis: BreakpointAnalysis):
        """打印执行摘要"""
        print(f"\n" + "="*60)
        print(f"断点复现执行摘要")
        print(f"="*60)
        print(f"场景ID: {scenario_id}")
        print(f"断点位置: 第 {breakpoint} 步")
        print(f"继续执行步数: {len(execution_steps)}")
        print(f"执行成功: {'是' if all(step.success for step in execution_steps) else '否'}")

        if comparison_summary.get("total_compared_steps", 0) > 0:
            print(f"\n对比分析:")
            print(f"  - 对比步数: {comparison_summary['total_compared_steps']}")
            print(f"  - 分歧率: {comparison_summary.get('divergence_rate', 0.0):.1%}")
            print(f"  - Agent匹配率: {comparison_summary.get('agent_match_rate', 0.0):.1%}")
            print(f"  - 内容相似度: {comparison_summary.get('average_content_similarity', 0.0):.1%}")

            first_divergence = comparison_summary.get('first_divergence_step')
            if first_divergence:
                print(f"  - 首次分歧步骤: 第 {first_divergence} 步")

        print(f"\n断点分析:")
        print(f"  - 推荐断点: {breakpoint_analysis.recommended_breakpoints}")
        print(f"  - 关键决策点: {breakpoint_analysis.critical_decision_points}")
        print(f"  - 状态转换点: {breakpoint_analysis.state_transition_points}")

        print(f"\n" + "="*60)

# ========================== 命令行接口 ==========================

async def main():
    """主函数：解析命令行参数并执行断点复现"""
    parser = argparse.ArgumentParser(
        description="增强版断点复现系统 - 从指定断点位置恢复并继续执行 MagenticOne 系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 从第12步断点开始复现
  python breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o

  # 自动选择最佳断点位置
  python breakpoint_reproduction.py --scenario 1 --breakpoint auto --model gpt-4o

  # 使用不同模型
  python breakpoint_reproduction.py --scenario 1 --breakpoint 10 --model gpt-4o-mini

功能特性:
  - 多层次状态恢复（消息历史、Agent状态、环境状态）
  - 智能断点分析和推荐
  - 实时执行监控和对比分析
  - 详细的结果保存和统计
        """
    )

    parser.add_argument(
        "--scenario",
        required=True,
        help="场景ID（如 1, 2, 3 等）"
    )

    parser.add_argument(
        "--breakpoint",
        required=True,
        help="断点位置（步骤编号）或 'auto' 自动选择最佳断点"
    )

    parser.add_argument(
        "--model",
        default=Config.DEFAULT_MODEL,
        help=f"使用的模型（默认: {Config.DEFAULT_MODEL}）"
    )

    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Config.OUTPUT_DIR,
        help=f"输出目录（默认: {Config.OUTPUT_DIR}）"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )

    parser.add_argument(
        "--analyze-only",
        action="store_true",
        help="仅进行断点分析，不执行复现"
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        Config.LOG_LEVEL = logging.DEBUG

    # 设置输出目录
    if args.output_dir:
        Config.OUTPUT_DIR = args.output_dir

    # 处理断点参数
    if args.breakpoint.lower() != "auto":
        try:
            breakpoint = int(args.breakpoint)
        except ValueError:
            print(f"❌ 无效的断点位置: {args.breakpoint}")
            print("断点位置必须是数字或 'auto'")
            return
    else:
        breakpoint = args.breakpoint

    try:
        # 创建断点复现系统
        reproduction_system = BreakpointReproductionSystem()

        if args.analyze_only:
            # 仅进行断点分析
            print("🔍 === 仅进行断点分析模式 ===")
            scenario_data = reproduction_system.scenario_loader.load_scenario(args.scenario)
            integrated_logs = reproduction_system.log_integrator.integrate_scenario_logs(
                args.scenario, args.model
            )
            breakpoint_analysis = reproduction_system.breakpoint_analyzer.analyze_breakpoints(
                scenario_data, integrated_logs
            )

            print("\n断点分析结果:")
            print(breakpoint_analysis.analysis_summary)
            print(f"\n推荐断点位置: {breakpoint_analysis.recommended_breakpoints}")

        else:
            # 执行完整的断点复现
            result_path = await reproduction_system.run_reproduction(
                args.scenario, breakpoint, args.model
            )
            print(f"\n✅ 断点复现完成！结果已保存到: {result_path}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(main())
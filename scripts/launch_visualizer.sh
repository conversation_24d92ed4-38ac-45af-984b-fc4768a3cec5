#!/bin/bash

# Multi-Agent Workflow Visualizer Launch Script
# 
# This script launches a Streamlit application for visualizing LLM-powered 
# multi-agent collaboration workflows from JSON log files.
#
# Prerequisites:
# - Python 3.11+ with conda
# - The 'ada' conda environment 
# - Log files in logs/generated/ directory with 'llm_calls' structure
#
# Usage:
#   ./launch_visualizer.sh
#
# The application will be available at: http://localhost:8501

echo "🤖 Starting Multi-Agent Workflow Visualizer..."
echo "================================================================"

# Navigate to the project directory
cd ~/scripts

# Check if ada environment exists
if conda info --envs | grep -q "ada"; then
    echo "✅ Found 'ada' conda environment"
else
    echo "❌ 'ada' conda environment not found"
    echo "Please create the conda environment first"
    exit 1
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
conda run -n ada python -c "import streamlit, plotly, pandas" 2>/dev/null || {
    echo "📦 Installing required packages..."
    conda run -n ada pip install streamlit plotly pandas numpy
}

# Launch Streamlit app
echo "🚀 Launching Streamlit application..."
echo "📍 URL: http://localhost:8501"
echo "📂 Log files: logs/generated/*_llm_calls_formatted.json"
echo "================================================================"
echo "Press Ctrl+C to stop the application"
echo ""

conda run -n ada streamlit run workflow_visualizer.py \
    --server.port 8501 \
    --server.address 0.0.0.0 \
    --server.headless true \
    --server.enableCORS false \
    --server.enableXsrfProtection false

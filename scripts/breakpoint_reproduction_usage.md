# 增强版断点恢复系统使用指南

## 概述

增强版断点恢复系统 (`breakpoint_reproduction.py`) 是一个强大的工具，用于从 MagenticOne 多智能体系统的特定执行点恢复并继续执行，特别适用于错误分析、行为复现和系统调试。

## 核心功能

### 1. 多层次状态恢复
- **消息历史恢复**: 注入断点前的所有对话历史
- **Agent 状态恢复**: 恢复各个智能体的内部状态
- **环境状态恢复**: 基于详细日志恢复执行环境
- **状态验证**: 确保恢复状态的一致性

### 2. 智能断点分析
- **自动断点推荐**: 基于执行历史分析最佳断点位置
- **关键决策点识别**: 找到影响执行流程的关键节点
- **错误传播链分析**: 追踪错误的产生和传播过程
- **智能体切换点检测**: 识别智能体间的交互转换

### 3. 实时执行监控
- **步骤级监控**: 记录每个执行步骤的详细信息
- **行为对比分析**: 与原始执行进行实时对比
- **差异检测**: 自动识别执行路径的差异
- **决策点标记**: 标记关键的决策节点

### 4. 详细日志集成
- **多源日志整合**: 整合主日志、LLM调用日志、详细执行日志
- **执行轨迹重建**: 基于日志重建完整的执行轨迹
- **状态快照**: 生成关键时刻的系统状态快照

## 使用方法

### 基础用法

```bash
# 从第12步恢复场景1的执行
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o

# 使用不同模型
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o-mini
```

### 智能断点分析

```bash
# 启用自动断点分析和推荐
python scripts/breakpoint_reproduction.py --scenario 1 --auto-breakpoint --model gpt-4o

# 结合手动断点和智能分析
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 10 --auto-breakpoint --model gpt-4o
```

### 详细日志分析

```bash
# 启用详细日志分析（默认启用）
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --analyze-logs --model gpt-4o

# 禁用执行监控（提高性能）
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --no-monitoring --model gpt-4o
```

## 输出文件说明

### 主要结果文件
```
logs/breakpoint_reproduction/scenario_1_breakpoint_12_gpt-4o_20250128_143022.json
```

包含完整的复现结果：
- 原始场景数据和失败信息
- 状态恢复统计信息
- 断点后的执行历史
- 执行监控和对比分析
- 智能断点分析结果

### 对比分析报告
```
logs/breakpoint_reproduction/comparison_report_1_12_gpt-4o_20250128_143022.json
```

包含详细的执行对比：
- 步骤级相似度分析
- 差异点详细记录
- 决策点标记
- 行为偏差统计

## 配置选项

### 环境变量
```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_API_BASE="your-api-base"  # 可选
```

### 配置参数
在代码中可以调整的配置：
- `ENABLE_LOG_ANALYSIS`: 启用日志分析
- `ENABLE_STATE_VALIDATION`: 启用状态验证
- `ENABLE_EXECUTION_MONITORING`: 启用执行监控
- `APPEND_MODE_LOGGING`: 使用追加模式保存日志
- `SAVE_DETAILED_LOGS`: 保存详细日志
- `SAVE_COMPARISON_ANALYSIS`: 保存对比分析

## 典型使用场景

### 1. 错误复现和分析
当你知道某个场景在第12步出错时：
```bash
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 11 --model gpt-4o
```
从错误前一步开始，观察是否能重现相同的错误。

### 2. 行为差异分析
对比不同模型在相同断点的行为：
```bash
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --model gpt-4o-mini
```

### 3. 智能断点探索
让系统自动找到最佳的断点位置：
```bash
python scripts/breakpoint_reproduction.py --scenario 1 --auto-breakpoint --model gpt-4o
```

### 4. 深度调试分析
启用所有分析功能进行深度调试：
```bash
python scripts/breakpoint_reproduction.py --scenario 1 --breakpoint 12 --auto-breakpoint --analyze-logs --model gpt-4o
```

## 注意事项

1. **日志文件依赖**: 详细日志分析需要 `logs/generated/` 目录中的相应日志文件
2. **内存使用**: 启用所有监控功能会增加内存使用
3. **执行时间**: 详细分析会增加执行时间
4. **API调用**: 每次复现都会产生新的API调用费用
5. **状态一致性**: 完全的状态恢复可能受到框架限制

## 故障排除

### 常见问题

1. **找不到日志文件**
   ```
   ⚠️ 日志目录不存在: logs/generated，将跳过日志分析功能
   ```
   解决：确保 `logs/generated/` 目录存在且包含相应的日志文件

2. **断点位置无效**
   ```
   ❌ 断点位置超出范围，最大值: 15，当前值: 20
   ```
   解决：检查场景文件中的历史记录长度，选择有效的断点位置

3. **状态恢复失败**
   ```
   [错误] 消息注入失败: ...
   ```
   解决：检查 MagenticOne 框架版本兼容性，或尝试不同的注入方式

### 调试技巧

1. 使用 `--no-monitoring` 禁用监控以提高性能
2. 先用小的断点值测试，确保基础功能正常
3. 检查输出文件中的 `restoration_stats` 了解恢复状态
4. 查看 `monitoring_summary` 了解执行差异

## 扩展开发

系统采用模块化设计，可以轻松扩展：

- `BreakpointAnalyzer`: 扩展断点分析算法
- `ExecutionMonitor`: 增加新的监控指标
- `LogDataIntegrator`: 支持新的日志格式
- `ResultSaver`: 添加新的输出格式

每个模块都有清晰的接口，便于独立开发和测试。

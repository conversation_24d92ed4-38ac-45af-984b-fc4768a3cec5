# 专注断点复现工具使用说明

## 概述

`focused_breakpoint_reproduction.py` 是一个专门用于从指定断点位置恢复并继续执行 MagenticOne 系统的工具。它可以帮助你分析系统在特定步骤之后的行为，用于调试和故障分析。

## 主要功能

### 🎯 核心功能
1. **状态恢复**: 从历史记录中恢复系统到指定断点位置
2. **继续执行**: 从断点位置开始继续执行推理过程
3. **详细记录**: 记录每个步骤的执行过程和结果
4. **结果保存**: 将完整的复现过程保存为结构化数据

### 📊 功能模块
- **ScenarioLoader**: 加载和验证失败场景数据
- **StateRestorer**: 将历史消息注入到系统运行时
- **BreakpointExecutor**: 从断点位置继续执行推理
- **ResultSaver**: 保存复现结果到文件

## 安装和配置

### 环境要求
```bash
# 确保已安装必要的依赖
pip install autogen-ext[magentic-one]
```

### 环境变量配置
```bash
# 必需：OpenAI API 密钥
export OPENAI_API_KEY="your-api-key-here"

# 可选：自定义 API 基础URL
export OPENAI_API_BASE="https://your-custom-endpoint.com"
```

### 目录结构
```
Multi_Agent/
├── Agents_Failure_Attribution/Who&When/Hand-Crafted/  # 场景数据目录
│   ├── 1.json
│   ├── 2.json
│   └── ...
├── logs/breakpoint_reproduction/                      # 输出目录
└── scripts/
    ├── focused_breakpoint_reproduction.py             # 主程序
    ├── test_focused_reproduction.py                   # 测试脚本
    └── README_focused_reproduction.md                 # 本文档
```

## 使用方法

### 基本用法
```bash
# 从场景1的第12步开始继续执行
python focused_breakpoint_reproduction.py --scenario 1 --breakpoint 12

# 指定使用的模型
python focused_breakpoint_reproduction.py --scenario 5 --breakpoint 8 --model gpt-4o-mini

# 查看帮助信息
python focused_breakpoint_reproduction.py --help
```

### 参数说明
- `--scenario`: 场景ID（必需），对应数据文件名（如 1 对应 1.json）
- `--breakpoint`: 断点位置（必需），从1开始计数的步骤编号
- `--model`: 使用的模型（可选），默认为 gpt-4o

### 运行测试
```bash
# 运行完整的测试套件
python test_focused_reproduction.py
```

## 输出结果

### 控制台输出
程序运行时会在控制台显示详细的执行过程：

```
🚀 === 断点复现任务开始 ===
场景ID: 1
断点位置: 第 12 步
使用模型: gpt-4o

=== 场景数据加载成功 ===
场景ID: 1
原始问题: Where can I take martial arts classes within a five-minute walk...
历史步数: 50
错误步骤: 25
错误原因: WebSurfer failed to find relevant information
出错智能体: WebSurfer

=== 开始状态恢复到断点 12 ===
[步骤  1] 注入消息 [human]: Where can I take martial arts classes within a five-minute...
  ✓ [步骤  1] 使用 _runtime.publish 注入成功
[步骤  2] 注入消息 [Orchestrator (thought)]: Initial plan: We are working to address...
  ✓ [步骤  2] 使用 _runtime.publish 注入成功
...

=== 从断点 12 继续执行 ===
原始问题: Where can I take martial arts classes within a five-minute walk from the New York Stock Exchange after work (7-9 pm)?
开始继续推理...

[步骤 13] [WebSurfer] I clicked 'https://dojos.info › NewYork › NewYork'...
[步骤 14] [Orchestrator (thought)] Updated Ledger: { "is_request_satisfied": {...
...

🎉 === 断点复现任务完成 ===
```

### 保存的文件
结果会保存为 JSON 文件，包含以下结构：

```json
{
  "scenario_id": "1",
  "reproduction_method": "focused_breakpoint_continuation",
  "breakpoint_step": 12,
  "model": "gpt-4o",
  "reproduced_at": "2025-07-28T10:30:45.123456Z",
  
  "original_task": {
    "question": "原始用户问题",
    "ground_truth": "期望答案",
    "total_original_steps": 50
  },
  
  "original_failure": {
    "mistake_step": 25,
    "mistake_reason": "错误原因描述",
    "mistake_agent": "WebSurfer"
  },
  
  "reproduction_stats": {
    "breakpoint_position": 12,
    "continuation_steps": 15,
    "total_reproduced_steps": 27,
    "injection_stats": {
      "total_attempted": 12,
      "successful": 12,
      "failed": 0
    }
  },
  
  "execution_history": {
    "full_history": [...],      // 完整执行历史
    "original_history": [...],  // 断点前的原始历史
    "continuation_only": [...]  // 从断点继续的新历史
  },
  
  "reproduction_config": {
    "inject_up_to_step": 12,
    "continue_from_step": 13,
    "model_config": {
      "model": "gpt-4o",
      "temperature": 0,
      "max_tokens": 512
    }
  }
}
```

## 代码架构

### 主要类说明

#### 1. Config
- **功能**: 集中管理所有配置参数
- **关键方法**: `validate()` - 验证配置有效性

#### 2. ScenarioLoader
- **功能**: 加载和验证失败场景数据
- **关键方法**: 
  - `load_scenario()` - 加载场景数据
  - `validate_breakpoint()` - 验证断点位置

#### 3. StateRestorer
- **功能**: 将历史消息注入到系统运行时
- **关键方法**:
  - `inject_single_message()` - 注入单条消息
  - `restore_to_breakpoint()` - 恢复到断点位置

#### 4. BreakpointExecutor
- **功能**: 从断点位置继续执行推理
- **关键方法**: `continue_from_breakpoint()` - 继续执行

#### 5. ResultSaver
- **功能**: 保存复现结果
- **关键方法**: `save_reproduction_result()` - 保存结果

#### 6. BreakpointReproducer
- **功能**: 主控制器，协调整个流程
- **关键方法**: `run_reproduction()` - 执行完整流程

## 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   ❌ 请设置 OPENAI_API_KEY 环境变量
   ```
   解决：确保正确设置了环境变量

2. **场景文件不存在**
   ```
   ❌ 场景文件不存在: Agents_Failure_Attribution/Who&When/Hand-Crafted/1.json
   ```
   解决：检查数据文件路径和场景ID

3. **断点位置无效**
   ```
   ❌ 断点位置超出范围，最大值: 50，当前值: 100
   ```
   解决：使用有效的断点位置（1到历史步数之间）

4. **消息注入失败**
   ```
   ⚠ 注入失败: 5 条消息
   ```
   解决：这通常不影响主要功能，但可能影响状态恢复的完整性

### 调试技巧

1. **使用测试脚本**：先运行 `test_focused_reproduction.py` 验证环境
2. **检查日志**：观察控制台输出中的详细执行过程
3. **验证输出**：检查生成的 JSON 文件内容
4. **从小断点开始**：使用较小的断点位置进行测试

## 扩展和定制

### 添加新的注入方法
在 `StateRestorer.inject_single_message()` 中添加新的注入方式：

```python
injection_methods = [
    ("_runtime.publish", lambda: self.agent._runtime.publish(message)),
    ("runtime.publish", lambda: self.agent.runtime.publish(message)),
    ("send", lambda: self.agent.send(message)),
    ("your_new_method", lambda: your_custom_injection(message))  # 添加这里
]
```

### 自定义输出格式
修改 `ResultSaver.save_reproduction_result()` 方法来改变输出格式。

### 添加新的验证逻辑
在相应的类中添加新的验证方法，例如在 `ScenarioLoader` 中添加数据完整性检查。

## 许可证

本工具是 Multi-Agent 系统调试工具集的一部分，遵循项目的开源许可证。

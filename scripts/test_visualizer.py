#!/usr/bin/env python3
"""
Test Script for Multi-Agent Workflow Visualizer

This script validates that log files can be properly parsed by the visualizer.
It checks for:
- Presence of log files directory
- Valid JSON structure in formatted log files
- Required fields (call_index, timestamp, agent_id) 
- LLM calls data structure

Expected Log File Format:
{
  "metadata": { ... },
  "llm_calls": [
    {
      "call_index": 1,
      "timestamp": "2025-07-27 03:26:42,413", 
      "agent_id": "MagenticOneOrchestrator_...",
      "prompt_tokens": 325,
      "completion_tokens": 298,
      "input_messages": [...],
      "response": { "model": "gpt-4o-mini-2024-07-18" }
    }
  ]
}

Compatible Files:
- *_llm_calls_formatted.json (contains LLM call data)

Incompatible Files (ignored):
- *_log_formatted.json (general log entries, different structure)

Usage:
    cd scripts
    python test_visualizer.py
    conda run -n m1 python test_visualizer.py

Author: GitHub Copilot  
Date: July 27, 2025
"""

import json
import sys
from pathlib import Path

def test_log_parsing():
    """Test that we can parse the log files correctly"""
    logs_dir = Path("../logs/generated")
    
    if not logs_dir.exists():
        print("❌ Logs directory not found")
        return False
    
    # Find formatted JSON files with LLM calls data
    all_files = [f for f in logs_dir.glob("*.json") if "formatted" in f.name]
    log_files = []
    
    # Pre-filter to only include files with llm_calls
    for file_path in all_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if 'llm_calls' in data and data['llm_calls']:
                log_files.append(file_path)
        except:
            continue
    
    if not log_files:
        print("❌ No LLM call log files found")
        return False
    
    print(f"✅ Found {len(log_files)} LLM call log files:")
    
    for log_file in log_files:
        print(f"  📄 {log_file.name}")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            llm_calls = data['llm_calls']
            print(f"    ✅ {len(llm_calls)} LLM calls found")
            
            # Check first call structure
            if llm_calls:
                first_call = llm_calls[0]
                required_fields = ['call_index', 'timestamp', 'agent_id']
                missing_fields = [f for f in required_fields if f not in first_call]
                
                if missing_fields:
                    print(f"    ⚠️  Missing fields in first call: {missing_fields}")
                else:
                    print(f"    ✅ Required fields present")
                    
                # Test timestamp parsing
                timestamp = first_call.get('timestamp', '')
                if timestamp:
                    print(f"    📅 First timestamp: {timestamp}")
                    
                # Test agent extraction
                agent_id = first_call.get('agent_id', '')
                if agent_id:
                    print(f"    🤖 First agent: {agent_id}")
                
        except Exception as e:
            print(f"    ❌ Error parsing file: {e}")
            continue
    
    print(f"\n📊 Summary: {len(log_files)} files are compatible with the visualizer")
    return True

if __name__ == "__main__":
    print("🧪 Testing Multi-Agent Workflow Visualizer")
    print("=" * 50)
    
    success = test_log_parsing()
    
    if success:
        print("\n✅ All tests passed! Ready to launch the visualizer.")
        print("\nTo start the application, run:")
        print("  ./launch_visualizer_2.sh")
        print("\nOr manually:")
        print("  conda run -n m1 streamlit run workflow_visualizer.py")
    else:
        print("\n❌ Some tests failed. Please check the log files.")
        sys.exit(1)

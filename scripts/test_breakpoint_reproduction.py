#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
test_breakpoint_reproduction.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
测试增强版断点恢复系统的功能

Usage:
    python scripts/test_breakpoint_reproduction.py
"""

import asyncio
import json
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from scripts.breakpoint_reproduction import (
    Config, EnhancedScenarioLoader, BreakpointAnalyzer, 
    EnhancedBreakpointReproducer, LogDataIntegrator
)

class BreakpointReproductionTester:
    """断点恢复系统测试器"""
    
    def __init__(self):
        self.test_results = []
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 === 断点恢复系统测试开始 ===\n")
        
        # 测试配置验证
        await self.test_config_validation()
        
        # 测试场景加载
        await self.test_scenario_loading()
        
        # 测试日志集成
        await self.test_log_integration()
        
        # 测试断点分析
        await self.test_breakpoint_analysis()
        
        # 测试完整流程（如果环境允许）
        if os.getenv("OPENAI_API_KEY"):
            await self.test_full_reproduction()
        else:
            print("⚠️  跳过完整流程测试（未设置 OPENAI_API_KEY）")
        
        # 输出测试结果
        self.print_test_summary()
    
    async def test_config_validation(self):
        """测试配置验证"""
        print("🔧 测试配置验证...")
        
        try:
            # 测试基础配置
            assert Config.DATA_DIR.exists(), f"数据目录不存在: {Config.DATA_DIR}"
            
            # 测试输出目录创建
            Config.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
            assert Config.OUTPUT_DIR.exists(), f"输出目录创建失败: {Config.OUTPUT_DIR}"
            
            self.test_results.append(("配置验证", "✅ 通过", "基础配置正常"))
            print("  ✅ 配置验证通过")
            
        except Exception as e:
            self.test_results.append(("配置验证", "❌ 失败", str(e)))
            print(f"  ❌ 配置验证失败: {e}")
    
    async def test_scenario_loading(self):
        """测试场景加载功能"""
        print("\n📁 测试场景加载...")
        
        try:
            loader = EnhancedScenarioLoader()
            
            # 测试加载场景1
            scenario_data = loader.load_scenario("1", include_logs=False)
            
            # 验证必要字段
            required_fields = ["history", "question", "mistake_step", "mistake_agent"]
            for field in required_fields:
                assert field in scenario_data, f"缺少字段: {field}"
            
            # 验证数据类型
            assert isinstance(scenario_data["history"], list), "history 应该是列表"
            assert len(scenario_data["history"]) > 0, "history 不应为空"
            
            # 测试断点验证
            loader.validate_breakpoint(scenario_data["history"], 5)
            
            self.test_results.append(("场景加载", "✅ 通过", f"成功加载场景1，包含{len(scenario_data['history'])}步"))
            print(f"  ✅ 场景加载通过，历史步数: {len(scenario_data['history'])}")
            
        except Exception as e:
            self.test_results.append(("场景加载", "❌ 失败", str(e)))
            print(f"  ❌ 场景加载失败: {e}")
    
    async def test_log_integration(self):
        """测试日志集成功能"""
        print("\n📊 测试日志集成...")
        
        try:
            if not Config.LOGS_DIR.exists():
                self.test_results.append(("日志集成", "⚠️  跳过", "日志目录不存在"))
                print("  ⚠️  跳过日志集成测试（日志目录不存在）")
                return
            
            integrator = LogDataIntegrator(Config.LOGS_DIR)
            
            # 查找场景1的日志文件
            log_files = integrator.find_log_files("1")
            
            if log_files:
                # 尝试加载详细日志
                log_data = integrator.load_detailed_logs("1")
                
                assert "execution_steps" in log_data, "缺少执行步骤数据"
                assert "llm_interactions" in log_data, "缺少LLM交互数据"
                
                self.test_results.append(("日志集成", "✅ 通过", f"找到{len(log_files)}个日志文件"))
                print(f"  ✅ 日志集成通过，找到日志文件: {list(log_files.keys())}")
            else:
                self.test_results.append(("日志集成", "⚠️  跳过", "未找到相关日志文件"))
                print("  ⚠️  未找到场景1的日志文件")
                
        except Exception as e:
            self.test_results.append(("日志集成", "❌ 失败", str(e)))
            print(f"  ❌ 日志集成失败: {e}")
    
    async def test_breakpoint_analysis(self):
        """测试断点分析功能"""
        print("\n🎯 测试断点分析...")
        
        try:
            loader = EnhancedScenarioLoader()
            scenario_data = loader.load_scenario("1", include_logs=False)
            
            analyzer = BreakpointAnalyzer(scenario_data)
            analysis = analyzer.analyze_breakpoints()
            
            # 验证分析结果
            assert hasattr(analysis, 'recommended_breakpoints'), "缺少推荐断点"
            assert hasattr(analysis, 'critical_decision_points'), "缺少关键决策点"
            assert hasattr(analysis, 'agent_transition_points'), "缺少智能体切换点"
            assert hasattr(analysis, 'analysis_summary'), "缺少分析摘要"
            
            self.test_results.append(("断点分析", "✅ 通过", 
                                    f"推荐{len(analysis.recommended_breakpoints)}个断点"))
            print(f"  ✅ 断点分析通过")
            print(f"    推荐断点: {analysis.recommended_breakpoints}")
            print(f"    决策点: {analysis.critical_decision_points}")
            print(f"    切换点: {analysis.agent_transition_points}")
            
        except Exception as e:
            self.test_results.append(("断点分析", "❌ 失败", str(e)))
            print(f"  ❌ 断点分析失败: {e}")
    
    async def test_full_reproduction(self):
        """测试完整的断点复现流程"""
        print("\n🚀 测试完整复现流程...")
        
        try:
            reproducer = EnhancedBreakpointReproducer()
            
            # 使用较小的断点进行测试
            output_path = await reproducer.run_reproduction(
                scenario_id="1",
                breakpoint=3,  # 使用较小的断点
                model="gpt-4o",
                auto_breakpoint=False,
                analyze_logs=False  # 禁用日志分析以加快测试
            )
            
            # 验证输出文件
            assert output_path.exists(), f"输出文件不存在: {output_path}"
            
            # 验证输出内容
            with output_path.open('r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            required_keys = [
                "scenario_id", "breakpoint_step", "model", 
                "execution_stats", "full_history"
            ]
            for key in required_keys:
                assert key in result_data, f"输出文件缺少字段: {key}"
            
            self.test_results.append(("完整复现", "✅ 通过", f"输出文件: {output_path.name}"))
            print(f"  ✅ 完整复现通过，输出文件: {output_path}")
            
        except Exception as e:
            self.test_results.append(("完整复现", "❌ 失败", str(e)))
            print(f"  ❌ 完整复现失败: {e}")
    
    def print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🧪 测试结果摘要")
        print("="*60)
        
        passed = 0
        failed = 0
        skipped = 0
        
        for test_name, status, details in self.test_results:
            print(f"{status} {test_name}: {details}")
            
            if "✅" in status:
                passed += 1
            elif "❌" in status:
                failed += 1
            elif "⚠️" in status:
                skipped += 1
        
        print("\n" + "-"*60)
        print(f"总计: {len(self.test_results)} 项测试")
        print(f"通过: {passed} 项")
        print(f"失败: {failed} 项")
        print(f"跳过: {skipped} 项")
        
        if failed == 0:
            print("\n🎉 所有测试通过！系统功能正常。")
        else:
            print(f"\n⚠️  有 {failed} 项测试失败，请检查相关功能。")

async def main():
    """主测试函数"""
    tester = BreakpointReproductionTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())

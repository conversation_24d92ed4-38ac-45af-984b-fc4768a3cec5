# Multi_Agent 项目说明 / Project Overview

本项目为多智能体系统的测试与实验环境，支持本地 Ollama (llama3.1) 和 OpenAI API。

## 环境配置 / Environment Setup

1. 安装依赖 Install Python dependencies:

```bash
conda create -n ada python=3.11 -y
conda activate ada
pip install -r requirements.txt  
```

2. 安装/更新本地 autogen-ext 包（开发模式）Install/Update local autogen-ext package (editable mode):

```bash
cd src/autogen-ext
pip install -e .
```

1. （可选）安装并配置 Ollama (如需本地大模型):

- 安装Ollama（以Linux为例）Install Ollama (Linux example):

```bash
curl -fsSL https://ollama.com/install.sh | sh
```

- 启动Ollama服务 Start Ollama service:

```bash
ollama serve
```

- 拉取llama3.1模型 Pull llama3.1 model:

```bash
ollama pull llama3.1:latest  
```

3. (可选) 配置OpenAI API Key (如需使用OpenAI):

```bash
export OPENAI_API_KEY=your_api_key_here
```

4. 安装 Playwright 及 Chromium 浏览器 (如需网页自动化):

```bash
pip install playwright  # 安装 Playwright Python 包
playwright install --with-deps chromium  # 安装 Chromium 及依赖
```

---

## 目录结构 / Directory Structure

```
.
├── scripts/
│   ├── run_m1_test.py         # 主测试脚本 Main test script
│   ├── workflow_visualizer.py # 多智能体工作流可视化工具 Multi-agent workflow visualizer
│   ├── test_visualizer.py     # 可视化工具测试脚本 Visualizer test script
│   ├── launch_visualizer.sh   # 可视化工具启动脚本 Visualizer launcher
│   └── utils.py              # 工具函数 Utility functions
├── schedule_records/
│   ├── 2025_07_23_work_log.md
│   ├── 2025_07_24_work_log.md
│   ├── 2025_07_25_work_log.md
│   └── 2025_07_26_work_log.md
├── logs/
│   └── generated/             # 自动生成的日志和trace文件
│       ├── *.log              # 原始日志文件
│       └── *_llm_calls_formatted.json  # 格式化的LLM调用日志（可视化用）
├── utils.py                   # 工具函数，包含日志解析和处理功能
├── Agents_Failure_Attribution/
│   └── Who&When/Hand-Crafted/ # 场景数据
├── README.md                  # 项目说明
```

## 快速开始 / Quick Start

运行主脚本 Run the main script:

```bash
python scripts/run_m1_test.py --scenario 1 --endpoint_type ollama --model llama3.1
python scripts/run_m1_test.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-20240513 # for cloudgpt, with vision
python scripts/run_m1_test.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-mini-20240718 # for cloudgpt, without vision, fast test/dev
```
- `--scenario` 指定场景ID (如 1)
- `--endpoint_type` 选择推理端: `ollama` (默认) 或 `openai` 或 `cloudgpt`
- `--model` 指定模型名称 (如 `llama3.1`、`gpt-4` 等，默认为 `llama3.1`)


## 日志分析 / Log Analysis

项目生成的日志文件包含以下类型的信息：

- **LLMCall日志**：记录LLM调用的输入输出、性能数据、token统计
- **HTTP Request日志**：记录与Ollama服务的网络通信状态  
- **Message日志**：记录多智能体系统内部的消息传递
- **系统操作日志**：记录GroupChatReset等系统级操作
- **警告日志**：记录系统警告和错误信息

使用 `utils.py` 中的工具函数可以解析和分析这些日志文件。

## 工作流可视化 / Workflow Visualization

项目提供了交互式的多智能体工作流可视化工具，用于分析和理解智能体协作过程。

### 功能特性 / Features
- **交互式时间线**：展示智能体间的协作时序 Interactive timeline of agent interactions
- **Token使用分析**：监控prompt和completion token的使用情况 Token usage analysis  
- **智能体活动模式**：分析各智能体的活跃度和交互模式 Agent activity patterns
- **详细调用检查**：深入查看单个LLM调用的详细信息 Detailed call inspection
- **多日志文件支持**：可切换分析不同的日志文件 Multiple log file support

### 快速启动 / Quick Start

```bash
# 进入脚本目录 Navigate to scripts directory
cd scripts

# 测试日志文件兼容性 Test log file compatibility  
conda run -n ada python test_visualizer.py

# 启动可视化工具 Launch visualizer
chmod +x launch_visualizer.sh
./launch_visualizer.sh

# 手动启动 Manual launch
conda run -n ada streamlit run workflow_visualizer.py
```

### 访问界面 / Access Interface
启动后在浏览器中访问 Access in browser: `http://localhost:8501`

### 兼容的日志文件 / Compatible Log Files
- `*_llm_calls_formatted.json` (位于 `logs/generated/` 目录)
- 包含 `llm_calls` 数据结构的JSON文件

### 依赖包 / Dependencies
可视化工具需要以下额外依赖 Additional dependencies for visualization:
```bash
conda run -n ada pip install streamlit plotly pandas numpy
```

## 注意事项 / Notes
- 本地 Ollama 推荐使用 llama3.1，部分版本可能有兼容性问题。
- 使用 OpenAI 需设置 `OPENAI_API_KEY` 环境变量。
- 日志和结果文件保存在 `logs/generated/` 目录。
- 日志文件以 `.log` 后缀保存，便于后续分析和调试。

---
